<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: login.php");
    exit;
}

$login_email = $_SESSION['login_email'];
$login_id = $_SESSION['login_id'];
$user_type = $_SESSION['user_type'];

// Database connection
$pdo = new PDO("sqlite:dmsdb.db");
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$errorMessage = '';
$successMessage = '';
$isExpired = isset($_GET['expired']) && $_GET['expired'] == '1';

// Handle password change form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];

    // Validate current password
    $stmt = $pdo->prepare("SELECT password FROM Users WHERE ID = :user_id");
    $stmt->bindParam(':user_id', $login_id);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user['password'] !== $currentPassword) {
        $errorMessage = "Current password is incorrect.";
    } elseif ($newPassword !== $confirmPassword) {
        $errorMessage = "New passwords do not match.";
    } elseif (strlen($newPassword) < 8) {
        $errorMessage = "Password must be at least 8 characters long.";
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $newPassword)) {
        $errorMessage = "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.";
    } else {
        // Check if new password is same as current
        if ($newPassword === $currentPassword) {
            $errorMessage = "New password cannot be the same as current password.";
        } else {
            // Check password history (prevent reuse of last 3 passwords)
            $historyStmt = $pdo->prepare("SELECT password FROM PasswordHistory WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 3");
            $historyStmt->bindParam(':user_id', $login_id);
            $historyStmt->execute();
            $previousPasswords = $historyStmt->fetchAll(PDO::FETCH_COLUMN);

            if (in_array($newPassword, $previousPasswords)) {
                $errorMessage = "You cannot reuse any of your last 3 passwords.";
            } else {
                // Update password
                $updateStmt = $pdo->prepare("UPDATE Users SET password = :password WHERE ID = :user_id");
                $updateStmt->bindParam(':password', $newPassword);
                $updateStmt->bindParam(':user_id', $login_id);
                
                if ($updateStmt->execute()) {
                    $successMessage = "Password changed successfully!";
                    
                    // Clear password warning and expired flags
                    unset($_SESSION['password_warning']);
                    unset($_SESSION['password_expired']);
                    
                    // Redirect to appropriate page after 2 seconds
                    if (isset($_SESSION['redirect_after_password_change'])) {
                        $redirectType = $_SESSION['redirect_after_password_change'];
                        unset($_SESSION['redirect_after_password_change']);
                        
                        if ($redirectType === 'Admin') {
                            echo "<script>
                                setTimeout(function() {
                                    window.location.href = 'admin.php';
                                }, 2000);
                            </script>";
                        } elseif ($redirectType === 'User') {
                            echo "<script>
                                setTimeout(function() {
                                    window.location.href = 'index.php';
                                }, 2000);
                            </script>";
                        } elseif ($redirectType === 'Dispatcher') {
                            echo "<script>
                                setTimeout(function() {
                                    window.location.href = 'status.php';
                                }, 2000);
                            </script>";
                        }
                    }
                } else {
                    $errorMessage = "Failed to update password. Please try again.";
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password</title>
    <link rel="icon" type="image/x-icon" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #57976a, #81c281);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #fff;
        }

        .password-box {
            width: 450px;
            padding: 40px;
            background: #fff;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            border-radius: 15px;
            color: #333;
        }

        .logo {
            width: 80px;
            margin-bottom: 20px;
        }

        h2 {
            margin-bottom: 20px;
            font-size: 28px;
            color: #333;
            font-weight: 600;
        }

        .alert {
            padding: 12px;
            margin-bottom: 20px;
            border-radius: 5px;
            text-align: center;
        }

        .alert.error {
            background-color: #f44336;
            color: white;
        }

        .alert.success {
            background-color: #4CAF50;
            color: white;
        }

        .alert.warning {
            background-color: #ff9800;
            color: white;
        }

        .textbox {
            margin-bottom: 20px;
            text-align: left;
        }

        .textbox label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .textbox input {
            width: 100%;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .textbox input:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.7);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 10px;
        }

        .btn:hover {
            background-color: #45a049;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .password-requirements {
            text-align: left;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        .password-requirements h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .password-requirements ul {
            margin-left: 20px;
            color: #666;
        }

        .password-requirements li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="password-box">
        <img src="logo-inner.png" alt="Logo" class="logo">
        
        <?php if ($isExpired): ?>
            <h2>Password Expired</h2>
            <div class="alert warning">
                Your password has expired. You must change it to continue.
            </div>
        <?php else: ?>
            <h2>Change Password</h2>
        <?php endif; ?>

        <?php if ($errorMessage): ?>
            <div class="alert error"><?php echo $errorMessage; ?></div>
        <?php endif; ?>

        <?php if ($successMessage): ?>
            <div class="alert success"><?php echo $successMessage; ?></div>
        <?php endif; ?>

        <form method="POST">
            <div class="textbox">
                <label for="current_password">Current Password:</label>
                <input type="password" id="current_password" name="current_password" required>
            </div>

            <div class="password-requirements">
                <h4>Password Requirements:</h4>
                <ul>
                    <li>At least 8 characters long</li>
                    <li>At least one uppercase letter (A-Z)</li>
                    <li>At least one lowercase letter (a-z)</li>
                    <li>At least one number (0-9)</li>
                    <li>At least one special character (@$!%*?&)</li>
                    <li>Cannot be same as current password</li>
                    <li>Cannot be same as last 3 passwords</li>
                </ul>
            </div>

            <div class="textbox">
                <label for="new_password">New Password:</label>
                <input type="password" id="new_password" name="new_password" required>
            </div>

            <div class="textbox">
                <label for="confirm_password">Confirm New Password:</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>

            <button type="submit" class="btn">Change Password</button>
            
            <?php if (!$isExpired): ?>
                <button type="button" class="btn btn-secondary" onclick="goBack()">Cancel</button>
            <?php endif; ?>
        </form>
    </div>

    <script>
        function goBack() {
            <?php if ($user_type === 'Admin'): ?>
                window.location.href = 'admin.php';
            <?php elseif ($user_type === 'User'): ?>
                window.location.href = 'index.php';
            <?php elseif ($user_type === 'Dispatcher'): ?>
                window.location.href = 'status.php';
            <?php endif; ?>
        }
    </script>
</body>
</html>
