# Status-Based Email Notification System for Document Management System

## Overview

This document describes the comprehensive status-based email notification system implemented for the Document Management System (DMS). The system provides centralized email functionality with threading support, enhanced visual templates, and status-specific notifications for document lifecycle events.

## Features

### ✅ **Implemented Features**

1. **Email Branding**
   - Uses `favicon.png` as the email sender logo/profile picture
   - Enhanced branded email templates with improved visual design
   - Professional email layout with responsive design and status-specific styling

2. **Email Threading**
   - Proper email headers (In-Reply-To, References) for conversation threading
   - All emails related to the same reference number are grouped together
   - Maintains conversation continuity across different document lifecycle events
   - Compatible with Gmail, Outlook, and other modern email clients

3. **Centralized Email Logic**
   - Single `EmailNotificationSystem` class in `email_notification.php`
   - Eliminates code duplication across different modules
   - Easy to maintain and extend

4. **Status-Based Notifications (ONLY on Status Changes)**
   - **Draft Status** (Status = 1): Document creation in `add.php`
   - **Signed Status** (Status = 2): Document signing in `edit.php`
   - **Dispatched Status** (Status = 3): Document dispatch in `dispatch.php` and `status.php`
   - **No emails** sent for regular document updates without status changes

5. **Enhanced Recipient Management**
   - Document creator (if email exists)
   - Document signatory (if email exists)
   - Users with document rights (if emails exist)
   - Union of all recipients with no duplicates

6. **Dispatch Information Integration**
   - **Post Dispatch**: Shows courier name and tracking ID
   - **Email Dispatch**: Shows email address used for dispatch
   - **Enhanced Templates**: Status-specific information and visual indicators

## File Structure

```
dms1/
├── email_notification.php          # Main email notification system
├── add.php                         # Modified to use centralized email system
├── edit.php                        # Modified to use centralized email system
├── dispatch.php                    # Modified to use centralized email system
├── status.php                      # Modified to use centralized email system
├── test_email_system.php           # Email system testing interface
├── test_email_handler.php          # Handler for email tests
├── favicon.png                     # Used as email sender logo
└── EMAIL_SYSTEM_README.md          # This documentation file
```

## Status-Based Email Templates

The system includes enhanced status-specific email templates:

### 1. Draft Status (Document Created)
- **Color**: Green (#28a745)
- **Trigger**: When document is created in `add.php` (Status = 1)
- **Message**: "A new document has been created and is now in Draft status"
- **Recipients**: Creator + Signatory + Assigned users

### 2. Signed Status (Document Approved)
- **Color**: Blue (#007bff)
- **Trigger**: When document status changes to Signed in `edit.php` (Status = 2)
- **Message**: "This document has been signed and approved by the signatory"
- **Recipients**: Creator + Signatory + Assigned users
- **Features**: Shows status transition from previous status to Signed

### 3. Dispatched Status (Document Sent)
- **Color**: Purple (#6f42c1)
- **Trigger**: When document status changes to Dispatched in `dispatch.php` or `status.php` (Status = 3)
- **Message**: Includes dispatch method and details
- **Recipients**: Creator + Signatory + Assigned users
- **Enhanced Features**:
  - **Post Dispatch**: Shows courier name and tracking ID with special formatting
  - **Email Dispatch**: Shows email address used for dispatch
  - **Visual Indicators**: Special styling for dispatch information
  - **Status Transition**: Shows change from Signed to Dispatched

### 4. Enhanced Visual Design
- **Status Badges**: Color-coded status indicators with rounded styling
- **Dispatch Information**: Highlighted sections with icons (📦, 📋, 🚚, 📍, 📧)
- **Improved Typography**: Better font hierarchy and spacing
- **Responsive Design**: Works on all email clients and devices

## Usage

### Basic Usage

```php
// Include the email notification system
require_once 'email_notification.php';

// Create an instance
$emailSystem = createEmailNotificationSystem($pdo);

// Send notifications
$result = $emailSystem->sendDocumentCreatedNotification($refNo);
$result = $emailSystem->sendDocumentEditedNotification($refNo);
$result = $emailSystem->sendDocumentDispatchedNotification($refNo, $courier, $trackingId);
$result = $emailSystem->sendStatusChangedNotification($refNo, $oldStatus, $newStatus);
```

### Advanced Usage

```php
// Send custom notification with additional data
$additionalData = [
    'Courier' => 'DHL Express',
    'Tracking ID' => 'DHL123456789'
];
$result = $emailSystem->sendNotification($refNo, 'dispatched', $additionalData);

// Test email configuration
$result = $emailSystem->testEmailConfiguration('<EMAIL>');
```

## Integration Points

The email system is integrated into the following files:

### 1. `add.php`
- **Trigger**: After successful document creation
- **Event**: `created`
- **Location**: After `store_docdetails()` and `assign_user_rights()`

### 2. `edit.php`
- **Trigger**: After successful document update
- **Event**: `edited`
- **Location**: After document update SQL execution

### 3. `dispatch.php`
- **Trigger**: After successful document dispatch
- **Event**: `dispatched`
- **Location**: After status update to "Dispatched"

### 4. `status.php`
- **Trigger**: After successful status change with tracking
- **Event**: `dispatched` (with courier and tracking info)
- **Location**: After DocTrack insertion

## Testing

### Test Interface
Access the test interface at: `test_email_system.php`

The test interface provides:
1. **Email Configuration Test** - Verifies SMTP settings
2. **Document Notification Test** - Tests specific event notifications
3. **Email Threading Test** - Verifies email threading functionality

### Manual Testing
```php
// Test basic email configuration
$emailSystem = createEmailNotificationSystem($pdo);
$result = $emailSystem->testEmailConfiguration('<EMAIL>');

// Test document notification
$result = $emailSystem->sendDocumentCreatedNotification('SKF-2025-0001');
```

## Configuration

### SMTP Settings
The email system uses the following SMTP configuration:
- **Host**: mail.synergy.net.pk
- **Port**: 465 (SSL)
- **Username**: <EMAIL>
- **Password**: Synergy@123

### Email Branding
- **Sender Name**: Synergy Computers (Pvt) Ltd
- **Sender Email**: <EMAIL>
- **Logo**: favicon.png (automatically included in emails)

## Error Handling

The system includes comprehensive error handling:
- Database connection errors
- SMTP configuration errors
- Missing document errors
- Recipient resolution errors
- Email sending failures

All errors are logged and returned in a structured format:
```php
[
    'success' => false,
    'sent_count' => 0,
    'total_recipients' => 0,
    'errors' => ['Error message 1', 'Error message 2'],
    'message' => 'Summary error message'
]
```

## Email Threading Implementation

The system implements proper email threading using:
1. **Message-ID**: Unique identifier for each email
2. **In-Reply-To**: References the root message of the thread
3. **References**: Contains all previous message IDs in the thread

This ensures that all emails related to the same document reference number appear as a conversation thread in email clients.

## Security Considerations

1. **Input Validation**: All inputs are validated and sanitized
2. **SQL Injection Prevention**: Uses prepared statements
3. **XSS Prevention**: HTML content is properly escaped
4. **Email Injection Prevention**: Email addresses are validated

## Maintenance

### Adding New Event Types
1. Add new event type to `getEventTitle()`, `getEventMessage()`, and `getEventColor()` methods
2. Create a new public method for the event (e.g., `sendDocumentArchivedNotification()`)
3. Integrate the new method into the appropriate PHP file

### Modifying Email Templates
Edit the `generateEmailTemplate()` method in `email_notification.php` to modify the email layout and styling.

### Updating Recipients Logic
Modify the `getRecipients()` method to change how recipients are determined for different events.

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check SMTP configuration
   - Verify network connectivity
   - Check email server logs

2. **Threading not working**
   - Ensure email client supports threading
   - Check Message-ID headers in sent emails
   - Verify In-Reply-To and References headers

3. **Recipients not receiving emails**
   - Check user email addresses in database
   - Verify user rights assignments
   - Check spam/junk folders

### Debug Mode
Enable debug mode by checking error logs or using the test interface to diagnose issues.

## Future Enhancements

Potential improvements for the email system:
1. Email templates customization interface
2. Email delivery status tracking
3. Email scheduling and queuing
4. Multiple language support
5. Email analytics and reporting
