<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: login.php");
    exit;
}

$login_email = $_SESSION['login_email'];
$login_id = $_SESSION['login_id'];
$user_type = $_SESSION['user_type'];

// Database connection
$pdo = new PDO("sqlite:dmsdb.db");
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Fetch user details with department and branch
$stmt = $pdo->prepare("
    SELECT u.*, 
           COALESCE(d.DeptName, 'Not Assigned') as department_name,
           COALESCE(b.City, 'Not Assigned') as branch_name,
           u.password_created_at,
           u.password_expires_at,
           u.last_login
    FROM Users u
    LEFT JOIN UsersDept ud ON u.ID = ud.user_id
    LEFT JOIN Departments d ON ud.dept = d.DeptCode
    LEFT JOIN UsersBranch ub ON u.ID = ub.user_id
    LEFT JOIN Branch b ON ub.branch = b.BCode
    WHERE u.ID = :user_id
");
$stmt->bindParam(':user_id', $login_id);
$stmt->execute();
$userDetails = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userDetails) {
    header("Location: login.php");
    exit;
}

// Calculate password expiry info
$today = new DateTime();
$passwordExpiry = new DateTime($userDetails['password_expires_at']);
$daysUntilExpiry = $today->diff($passwordExpiry)->days;
$isExpired = $passwordExpiry < $today;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile</title>
    <link rel="icon" type="image/x-icon" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f1f4f7;
            color: #333;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 80px;
            background: #357c3c;
            color: white;
            padding: 0 30px;
            border-radius: 0;
        }

        .header-left img {
            max-height: 40px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-email {
            font-weight: bold;
        }

        .logout {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .logout:hover {
            background-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .profile-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .profile-header {
            background: #357c3c;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .profile-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .profile-header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .profile-content {
            padding: 30px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-item {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #57976a;
        }

        .info-item label {
            font-weight: bold;
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-item value {
            display: block;
            font-size: 18px;
            color: #333;
            margin-top: 5px;
        }

        .password-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .password-info.expired {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .password-info.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }

        .password-info.good {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: #357c3c;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2c5f2f;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        @media (max-width: 768px) {
            header {
                flex-direction: column;
                height: auto;
                padding: 20px;
                text-align: center;
            }

            .header-right {
                margin-top: 15px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-left">
            <img src="logo-inner.png" alt="Logo" class="logo">
        </div>
        <div class="header-right">
            <span class="user-email"><?php echo htmlspecialchars($login_email); ?></span>
            <button class="logout" onclick="logout()">Logout</button>
        </div>
    </header>

    <div class="container">
        <div class="profile-card">
            <div class="profile-header">
                <h1><?php echo htmlspecialchars($userDetails['name']); ?></h1>
                <p><?php echo htmlspecialchars($userDetails['Type']); ?></p>
            </div>

            <div class="profile-content">
                <div class="info-grid">
                    <div class="info-item">
                        <label>Full Name</label>
                        <value><?php echo htmlspecialchars($userDetails['name']); ?></value>
                    </div>
                    <div class="info-item">
                        <label>Email Address</label>
                        <value><?php echo htmlspecialchars($userDetails['email']); ?></value>
                    </div>
                    <div class="info-item">
                        <label>User Type</label>
                        <value><?php echo htmlspecialchars($userDetails['Type']); ?></value>
                    </div>
                    <div class="info-item">
                        <label>Department</label>
                        <value><?php echo htmlspecialchars($userDetails['department_name']); ?></value>
                    </div>
                    <div class="info-item">
                        <label>Branch</label>
                        <value><?php echo htmlspecialchars($userDetails['branch_name']); ?></value>
                    </div>
                    <div class="info-item">
                        <label>Last Login</label>
                        <value><?php echo $userDetails['last_login'] ? date('M d, Y H:i', strtotime($userDetails['last_login'])) : 'Never'; ?></value>
                    </div>
                </div>

                <!-- Password Information -->
                <div class="password-info <?php echo $isExpired ? 'expired' : ($daysUntilExpiry <= 7 ? 'warning' : 'good'); ?>">
                    <h3>Password Information</h3>
                    <p><strong>Password Created:</strong> <?php echo date('M d, Y', strtotime($userDetails['password_created_at'])); ?></p>
                    <p><strong>Password Expires:</strong> <?php echo date('M d, Y', strtotime($userDetails['password_expires_at'])); ?></p>
                    
                    <?php if ($isExpired): ?>
                        <p style="color: #721c24; font-weight: bold;">⚠️ Your password has expired! You must change it immediately.</p>
                    <?php elseif ($daysUntilExpiry <= 7): ?>
                        <p style="color: #856404; font-weight: bold;">⚠️ Your password will expire in <?php echo $daysUntilExpiry; ?> day(s).</p>
                    <?php else: ?>
                        <p style="color: #155724; font-weight: bold;">✅ Your password is valid for <?php echo $daysUntilExpiry; ?> more days.</p>
                    <?php endif; ?>
                </div>

                <div class="action-buttons">
                    <a href="change_password.php" class="btn btn-primary">Change Password</a>
                    <a href="javascript:void(0)" onclick="goBack()" class="btn btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function logout() {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'logout.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function () {
                if (xhr.status === 200) {
                    window.location.href = 'login.php';
                } else {
                    console.error('Failed to log out');
                }
            };
            xhr.send();
        }

        function goBack() {
            <?php if ($user_type === 'Admin'): ?>
                window.location.href = 'admin.php';
            <?php elseif ($user_type === 'User'): ?>
                window.location.href = 'index.php';
            <?php elseif ($user_type === 'Dispatcher'): ?>
                window.location.href = 'status.php';
            <?php endif; ?>
        }
    </script>
</body>
</html>
