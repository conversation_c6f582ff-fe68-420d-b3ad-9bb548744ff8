-- Add password management columns (without DEFAULT for CURRENT_TIMESTAMP)
ALTER TABLE Users ADD COLUMN password_created_at DATETIME;
ALTER TABLE Users ADD COLUMN password_expires_at DATETIME;
ALTER TABLE Users ADD COLUMN last_login DATETIME;

-- Update existing users with initial password dates and expiry (3 months fixed)
UPDATE Users SET 
    password_created_at = CURRENT_TIMESTAMP,
    password_expires_at = datetime(CURRENT_TIMESTAMP, '+3 months')
WHERE password_created_at IS NULL;

-- Create password history table
CREATE TABLE IF NOT EXISTS "PasswordHistory" (
    "ID" INTEGER PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "password" TEXT NOT NULL,
    "created_at" DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(ID)
);

-- Create user audit table
CREATE TABLE IF NOT EXISTS "UserAudit" (
    "ID" INTEGER PRIMARY KEY AUTOINCREMENT,
    "user_id" INTEGER NOT NULL,
    "changed_by" INTEGER NOT NULL,
    "field_changed" TEXT NOT NULL,
    "old_value" TEXT,
    "new_value" TEXT,
    "change_type" TEXT NOT NULL,
    "changed_at" DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(ID),
    FOREIGN KEY (changed_by) REFERENCES Users(ID)
);

-- Create triggers for automatic auditing

-- Trigger to log password changes in PasswordHistory and UserAudit
CREATE TRIGGER IF NOT EXISTS after_password_update
AFTER UPDATE ON Users
FOR EACH ROW
WHEN OLD.password <> NEW.password
BEGIN
    -- Insert into PasswordHistory when password changes
    INSERT INTO PasswordHistory (user_id, password, created_at)
    VALUES (NEW.ID, OLD.password, COALESCE(OLD.password_created_at, CURRENT_TIMESTAMP));
    
    -- Insert into UserAudit for password change event
    INSERT INTO UserAudit (user_id, changed_by, field_changed, old_value, new_value, change_type, changed_at)
    VALUES (NEW.ID, NEW.ID, 'password', '[HIDDEN]', '[HIDDEN]', 'password_change', CURRENT_TIMESTAMP);
    
    -- Reset password dates (3 months fixed)
    UPDATE Users SET 
        password_created_at = CURRENT_TIMESTAMP,
        password_expires_at = datetime(CURRENT_TIMESTAMP, '+3 months')
    WHERE ID = NEW.ID;
END;

-- Trigger to log profile updates (name, email, type)
CREATE TRIGGER IF NOT EXISTS after_user_profile_update
AFTER UPDATE ON Users  
FOR EACH ROW
WHEN OLD.name <> NEW.name OR OLD.email <> NEW.email OR OLD.Type <> NEW.Type
BEGIN
    INSERT INTO UserAudit (user_id, changed_by, field_changed, old_value, new_value, change_type, changed_at)
    VALUES (NEW.ID, NEW.ID, 
           CASE 
               WHEN OLD.name <> NEW.name THEN 'name'
               WHEN OLD.email <> NEW.email THEN 'email'  
               WHEN OLD.Type <> NEW.Type THEN 'type'
           END,
           CASE 
               WHEN OLD.name <> NEW.name THEN OLD.name
               WHEN OLD.email <> NEW.email THEN OLD.email
               WHEN OLD.Type <> NEW.Type THEN OLD.Type
           END,
           CASE 
               WHEN OLD.name <> NEW.name THEN NEW.name
               WHEN OLD.email <> NEW.email THEN NEW.email
               WHEN OLD.Type <> NEW.Type THEN NEW.Type
           END,
           'profile_update', CURRENT_TIMESTAMP);
END;
