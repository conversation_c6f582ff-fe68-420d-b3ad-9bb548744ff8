-- Add active column to Departments table
ALTER TABLE Departments ADD COLUMN active INTEGER DEFAULT 1;

-- Add active column to Branch table  
ALTER TABLE Branch ADD COLUMN active INTEGER DEFAULT 1;

-- Add active column to Addresses table
ALTER TABLE Addresses ADD COLUMN active INTEGER DEFAULT 1;

-- Add active column to Signatory table
ALTER TABLE Signatory ADD COLUMN active INTEGER DEFAULT 1;

-- Update all existing records to be active by default
UPDATE Departments SET active = 1;
UPDATE Branch SET active = 1; 
UPDATE Addresses SET active = 1;
UPDATE Signatory SET active = 1;