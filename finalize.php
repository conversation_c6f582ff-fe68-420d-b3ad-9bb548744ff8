<?php
session_start();

// Include the centralized email notification system
require_once 'email_notification.php';

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: login.php");
    exit;
}

$login_email = $_SESSION['login_email'];
$login_id = $_SESSION['login_id'];
$user_type = $_SESSION['user_type'];

// Database connection
$dbFile = 'dmsdb.db';
$pdo = new PDO("sqlite:$dbFile");
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Pagination logic
$itemsPerPage = 10;  // Number of items per page
$currentPage = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$startIndex = ($currentPage - 1) * $itemsPerPage;

// Handle file upload and status change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['refno'])) {
    $refNo = $_POST['refno'];
    
    // Verify document is in Signed status (2)
    $checkStatusQuery = "SELECT Status FROM DocDetails WHERE RefNo = :refNo";
    $statusStmt = $pdo->prepare($checkStatusQuery);
    $statusStmt->execute([':refNo' => $refNo]);
    $currentStatus = $statusStmt->fetchColumn();
    
    if ($currentStatus != 2) {
        $errorMessage = "Document is not in Signed status.";
    } else if (!isset($_FILES['final_document']) || $_FILES['final_document']['error'] !== UPLOAD_ERR_OK) {
        $errorMessage = "No file uploaded or upload error occurred.";
    } else {
        // File size validation (10MB limit)
        $maxFileSize = 10 * 1024 * 1024; // 10MB in bytes
        if ($_FILES['final_document']['size'] > $maxFileSize) {
            $errorMessage = "File size exceeds the 10MB limit.";
        } else {
            try {
                $pdo->beginTransaction();
                
                // Create final directory if it doesn't exist
                $uploadDir = "uploads/" . $refNo . "/final/";
                if (!file_exists($uploadDir)) {
                    if (!mkdir($uploadDir, 0777, true)) {
                        throw new Exception("Failed to create directory structure.");
                    }
                }
                
                // Process file upload
                $file = $_FILES['final_document'];
                $tmpName = $file['tmp_name'];
                $fileName = basename($file['name']);
                $uploadPath = $uploadDir . $fileName;
                
                // Check for duplicate filenames
                $originalFileName = $fileName;
                $fileCounter = 1;
                while (file_exists($uploadPath)) {
                    $fileName = pathinfo($originalFileName, PATHINFO_FILENAME) . '-' . $fileCounter . '.' . pathinfo($originalFileName, PATHINFO_EXTENSION);
                    $uploadPath = $uploadDir . $fileName;
                    $fileCounter++;
                }
                
                // Move the uploaded file
                if (!move_uploaded_file($tmpName, $uploadPath)) {
                    throw new Exception("Failed to move uploaded file.");
                }
                
                // Insert file record
                $insertFileQuery = "INSERT INTO DocFiles (RefNo, FileName) VALUES (:refNo, :fileName)";
                $insertFileStmt = $pdo->prepare($insertFileQuery);
                $insertFileStmt->execute([
                    ':refNo' => $refNo,
                    ':fileName' => 'final/' . $fileName
                ]);
                
                // Update document status to "Ready For Dispatch" (3)
                $updateStatusQuery = "UPDATE DocDetails SET Status = 3, UpdatedBy = :updatedBy WHERE RefNo = :refNo";
                $updateStatusStmt = $pdo->prepare($updateStatusQuery);
                $updateStatusStmt->execute([
                    ':updatedBy' => $login_id,
                    ':refNo' => $refNo
                ]);
                
                $pdo->commit();
                
                // Send email notification
                try {
                    $emailSystem = createEmailNotificationSystem($pdo);
                    $result = $emailSystem->sendReadyForDispatchNotification($refNo, 'Signed');
                    
                    if ($result['success']) {
                        $successMessage = "Document finalized successfully! Email notifications sent to " . $result['sent_count'] . " recipients.";
                    } else {
                        $successMessage = "Document finalized successfully! However, there were issues sending email notifications: " . $result['message'];
                    }
                } catch (Exception $e) {
                    $successMessage = "Document finalized successfully! However, email notification failed: " . $e->getMessage();
                }
                
            } catch (Exception $e) {
                $pdo->rollBack();
                $errorMessage = "Error: " . $e->getMessage();
            }
        }
    }
}

// Fetch signed documents initiated by the current user
$query = "
    SELECT d.RefNo, d.Subject, d.Date, d.Addresse, u.email AS Signatory, 
           MAX(sa.ChangedAt) AS SignedDate
    FROM DocDetails d
    JOIN StatusAudit sa ON d.RefNo = sa.RefNo
    JOIN Signatory s ON d.Signatory = s.ID
    JOIN Users u ON u.ID = s.user_id
    JOIN UserRights ur ON d.RefNo = ur.refno
    WHERE d.Status = 2 
    AND ur.user_id = :user_id
    AND ur.rights = 4
    GROUP BY d.RefNo
    ORDER BY sa.ChangedAt DESC
    LIMIT :limit OFFSET :offset
";

$stmt = $pdo->prepare($query);
$stmt->bindValue(':user_id', $login_id, PDO::PARAM_INT);
$stmt->bindValue(':limit', $itemsPerPage, PDO::PARAM_INT);
$stmt->bindValue(':offset', $startIndex, PDO::PARAM_INT);
$stmt->execute();
$documents = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Count total documents for pagination
$countQuery = "
    SELECT COUNT(*) as total
    FROM DocDetails d
    JOIN UserRights ur ON d.RefNo = ur.refno
    WHERE d.Status = 2 
    AND ur.user_id = :user_id
    AND ur.rights = 4
";
$countStmt = $pdo->prepare($countQuery);
$countStmt->bindValue(':user_id', $login_id, PDO::PARAM_INT);
$countStmt->execute();
$totalItems = $countStmt->fetchColumn();
$totalPages = ceil($totalItems / $itemsPerPage);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finalize Documents</title>
    <link rel="icon" type="image/x-icon" href="favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f1f4f7;
            color: #333;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100px;
            background: linear-gradient(135deg, #57976a, #81c281);
            color: white;
            padding: 0 30px;
            border-radius: 8px;
            position: relative;
        }
        
        .header-left img {
            max-height: 40px;
        }
        
        .header-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .header-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-email {
            font-weight: bold;
            margin-right: 30px;
        }
        
        .logout {
            background-color: #d2d9dc;
            color: #357c3c;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .logout:hover {
            background-color: #357c3c;
            color: white;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            background-color: rgb(255, 255, 255);
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            margin: 20px 30px;
            color: #333;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 30px;
            border-radius: 8px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .datatable-container {
            padding: 25px;
            background-color: white;
            margin: 30px auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 1200px;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        table th, table td {
            padding: 15px;
            border: 1px solid #ddd;
            text-align: left;
            font-size: 14px;
        }
        
        table th {
            background-color: #d2d9dc;
            font-weight: bold;
        }
        
        table td {
            background-color: #f9f9f9;
        }
        
        .action-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .action-btn:hover {
            background-color: #45a049;
        }
        
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #666;
        }
        
        .pagination-controls {
            display: flex;
            gap: 10px;
        }
        
        .pagination-controls button {
            padding: 8px 15px;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .pagination-controls button:hover:not(:disabled) {
            background-color: #ddd;
        }
        
        .pagination-controls button:disabled {
            background-color: #f9f9f9;
            color: #aaa;
            cursor: not-allowed;
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow: auto;
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            width: 50%;
            max-width: 600px;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .modal-header h2 {
            font-size: 24px;
            color: #333;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #333;
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .file-upload-container {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        
        .file-upload-container:hover {
            border-color: #aaa;
        }
        
        .file-upload-container i {
            font-size: 48px;
            color: #aaa;
            margin-bottom: 10px;
        }
        
        .file-upload-container p {
            margin-bottom: 10px;
            color: #666;
        }
        
        .file-upload-btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .file-upload-btn:hover {
            background-color: #45a049;
        }
        
        .file-name {
            margin-top: 10px;
            font-size: 14px;
            color: #333;
            word-break: break-all;
        }
        
        .submit-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            transition: background-color 0.3s;
        }
        
        .submit-btn:hover {
            background-color: #45a049;
        }
        
        .cancel-btn {
            background-color: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
            transition: background-color 0.3s;
        }
        
        .cancel-btn:hover {
            background-color: #ddd;
        }
        
        @media (max-width: 768px) {
            header {
                flex-direction: column;
                height: auto;
                padding: 20px;
            }
            
            .header-right {
                margin-top: 20px;
                align-items: center;
            }
            
            .header-info {
                flex-direction: column;
                gap: 10px;
            }
            
            .user-email {
                margin-right: 0;
            }
            
            .modal-content {
                width: 90%;
                margin: 20% auto;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-left">
            <img src="logo-inner.png" alt="Logo" class="logo">
        </div>
        <div class="header-right">
            <div class="header-info">
                <span class="user-email"><?php echo htmlspecialchars($login_email ?? 'Guest'); ?></span>
                <button class="logout" onclick="openProfile()">Profile</button>
                <button class="logout" onclick="logout()">Logout</button>
            </div>
        </div>
    </header>
    
    <h1 class="page-title">Finalize Signed Documents</h1>
    
    <?php if (isset($successMessage)): ?>
        <div class="alert alert-success"><?php echo $successMessage; ?></div>
    <?php endif; ?>
    
    <?php if (isset($errorMessage)): ?>
        <div class="alert alert-danger"><?php echo $errorMessage; ?></div>
    <?php endif; ?>
    
    <div class="navigation">
        <div>
            <button class="logout" onclick="goBack()">Back to Dashboard</button>
        </div>
    </div>
    
    <div class="datatable-container">
        <?php if (count($documents) > 0): ?>
            <table id="datatable">
                <thead>
                    <tr>
                        <th>Reference No</th>
                        <th>Subject</th>
                        <th>Date</th>
                        <th>Addressee</th>
                        <th>Signatory</th>
                        <th>Signed Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($documents as $doc): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($doc['RefNo']); ?></td>
                            <td><?php echo htmlspecialchars($doc['Subject']); ?></td>
                            <td><?php echo date('Y-m-d', strtotime($doc['Date'])); ?></td>
                            <td><?php echo htmlspecialchars($doc['Addresse']); ?></td>
                            <td><?php echo htmlspecialchars($doc['Signatory']); ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($doc['SignedDate'])); ?></td>
                            <td>
                                <button class="action-btn" onclick="openFinalizeModal('<?php echo htmlspecialchars($doc['RefNo']); ?>')">
                                    Finalize
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <div class="pagination-info">
                        Showing <?php echo $startIndex + 1; ?> to <?php echo min($startIndex + $itemsPerPage, $totalItems); ?> of <?php echo $totalItems; ?> documents
                    </div>
                    <div class="pagination-controls">
                        <button onclick="window.location.href='?page=<?php echo max(1, $currentPage - 1); ?>'" <?php echo $currentPage == 1 ? 'disabled' : ''; ?>>
                            Previous
                        </button>
                        <button onclick="window.location.href='?page=<?php echo min($totalPages, $currentPage + 1); ?>'" <?php echo $currentPage == $totalPages ? 'disabled' : ''; ?>>
                            Next
                        </button>
                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <p>No signed documents found that require finalization.</p>
        <?php endif; ?>
    </div>
    
    <!-- Finalize Document Modal -->
    <div id="finalizeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Finalize Document</h2>
                <span class="close" onclick="closeFinalizeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="finalizeForm" method="POST" enctype="multipart/form-data">
                    <input type="hidden" id="refno" name="refno">
                    
                    <div class="form-group">
                        <label>Reference Number:</label>
                        <div id="displayRefNo" style="font-weight: bold;"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="final_document">Upload Final Document (Max 10MB):</label>
                        <div class="file-upload-container">
                            <i class="fas fa-file-upload"></i>
                            <p>Drag & drop your file here or</p>
                            <label class="file-upload-btn">
                                Browse Files
                                <input type="file" id="final_document" name="final_document" style="display: none;" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required onchange="updateFileName(this)">
                            </label>
                            <div id="fileName" class="file-name"></div>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-btn">Submit Final Document</button>
                    <button type="button" class="cancel-btn" onclick="closeFinalizeModal()">Cancel</button>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        // Open finalize modal
        function openFinalizeModal(refNo) {
            document.getElementById('refno').value = refNo;
            document.getElementById('displayRefNo').textContent = refNo;
            document.getElementById('finalizeModal').style.display = 'block';
        }
        
        // Close finalize modal
        function closeFinalizeModal() {
            document.getElementById('finalizeModal').style.display = 'none';
            document.getElementById('finalizeForm').reset();
            document.getElementById('fileName').textContent = '';
        }
        
        // Update file name display
        function updateFileName(input) {
            const fileName = input.files[0] ? input.files[0].name : '';
            document.getElementById('fileName').textContent = fileName;
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('finalizeModal');
            if (event.target == modal) {
                closeFinalizeModal();
            }
        }
        
        // Navigation functions
        function goBack() {
            <?php if ($user_type === 'Admin'): ?>
                window.location.href = 'admin.php';
            <?php else: ?>
                window.location.href = 'index.php';
            <?php endif; ?>
        }
        
        function openProfile() {
            window.location.href = 'profile.php';
        }
        
        function logout() {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'logout.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    window.location.href = 'login.php';
                } else {
                    console.error('Failed to log out');
                }
            };
            xhr.send();
        }
    </script>
</body>
</html>
