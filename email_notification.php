<?php
/**
 * Email Notification System for Document Management System
 *
 * This module provides centralized email functionality with threading support
 * and branded email templates for all document lifecycle events.
 *
 * Features:
 * - Email threading with proper headers (In-Reply-To, References)
 * - Branded emails with favicon.png as sender logo
 * - Event-specific email templates
 * - Recipient management based on user rights
 * - Conversation continuity across document lifecycle
 */

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require_once 'vendor/autoload.php';

class EmailNotificationSystem {

    private $pdo;
    private $mail;
    private $baseUrl;
    private $faviconPath;

    // ========================================
    // TESTING CONFIGURATION
    // ========================================
    // Set to true to enable test email copies for development/testing
    // Set to false or comment out for production deployment
    private const ENABLE_TEST_EMAIL = true;
    
    private const TEST_EMAIL_ADDRESS = '<EMAIL>';
    private const TEST_EMAIL_NAME = 'Test Admin';

    // Alternative configuration method - uncomment to disable test emails:
    // private const ENABLE_TEST_EMAIL = false;

    // ========================================
    // END TESTING CONFIGURATION
    // ========================================

    // Email threading storage - keeps track of message IDs for each reference number
    private static $messageThreads = [];

    public function __construct($pdo, $baseUrl = '') {
        $this->pdo = $pdo;
        $this->baseUrl = $baseUrl ?: $this->getCurrentBaseUrl();
        $this->faviconPath = $this->baseUrl . 'logo-inner.png';
        $this->initializeMailer();

        // Load environment variables
        if (class_exists('Dotenv\Dotenv')) {
            $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
            $dotenv->safeLoad();
        }
    }

    /**
     * Initialize PHPMailer with default settings
     */
    private function initializeMailer() {
        $this->mail = new PHPMailer(true);
        $this->mail->isSMTP();
        $this->mail->SMTPAuth = true;
        $this->mail->SMTPSecure = 'ssl';
        $this->mail->Host = "mail.synergy.net.pk";
        $this->mail->Port = 465;
        $this->mail->isHTML(true);
        $this->mail->CharSet = 'UTF-8';
        $this->mail->Username = '<EMAIL>';
        $this->mail->Password = 'Synergy@123';
        $this->mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => false
            )
        );

        // Set sender with favicon as profile picture
        $this->mail->setFrom('<EMAIL>', 'Synergy Computers (Pvt) Ltd');

        // Add custom headers for branding
        $this->mail->addCustomHeader('X-Mailer', 'Synergy DMS v1.0');
        $this->mail->addCustomHeader('X-Priority', '3');
    }

    /**
     * Get current base URL for favicon and links
     */
    private function getCurrentBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['SCRIPT_NAME'] ?? '');
        return $protocol . '://' . $host . $path;
    }

    /**
     * Generate email threading headers for conversation continuity
     */
    private function generateThreadingHeaders($refNo, $eventType) {
        $messageId = '<' . $refNo . '.' . $eventType . '.' . time() . '@synergy.net.pk>';

        // Store the first message ID for this reference number
        if (!isset(self::$messageThreads[$refNo])) {
            self::$messageThreads[$refNo] = [
                'root_message_id' => $messageId,
                'messages' => []
            ];
        }

        // Add current message to thread
        self::$messageThreads[$refNo]['messages'][] = $messageId;

        // Set threading headers
        $this->mail->MessageID = $messageId;

        // If this is not the first message in the thread, add threading headers
        if (count(self::$messageThreads[$refNo]['messages']) > 1) {
            $rootMessageId = self::$messageThreads[$refNo]['root_message_id'];
            $previousMessages = implode(' ', self::$messageThreads[$refNo]['messages']);

            $this->mail->addCustomHeader('In-Reply-To', $rootMessageId);
            $this->mail->addCustomHeader('References', $previousMessages);
        }

        return $messageId;
    }

    /**
     * Get document details for email content with dispatch information
     */
    private function getDocumentDetails($refNo) {
        $query = "
            SELECT
                d.*,
                dept.DeptName as DepartmentName,
                branch.City as BranchName,
                COALESCE(addr.name, d.Addresse) as Addresse,
                signatory_user.name as SignatoryName,
                signatory_user.email as SignatoryEmail,
                creator_user.name as CreatorName,
                creator_user.email as CreatorEmail,
                status.status as StatusName,
                dt.Courier,
                dt.TrackingId,
                dt.UpdatedBy as DispatchUpdatedBy
            FROM DocDetails d
            LEFT JOIN Departments dept ON d.Department = dept.DeptCode
            LEFT JOIN branch ON d.SendTo = branch.BCode
            LEFT JOIN Addresses addr ON d.Addresse = addr.code
            LEFT JOIN Signatory sig ON d.Signatory = sig.ID
            LEFT JOIN users signatory_user ON sig.user_id = signatory_user.ID
            LEFT JOIN UserRights ur ON d.RefNo = ur.RefNo AND ur.rights = 4
            LEFT JOIN users creator_user ON ur.user_id = creator_user.ID
            LEFT JOIN status ON d.Status = status.id
            LEFT JOIN DocTrack dt ON d.RefNo = dt.RefNo
            WHERE d.RefNo = ?
        ";

        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$refNo]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get status name by status ID
     */
    private function getStatusName($statusId) {
        switch ($statusId) {
            case 1: return 'Draft';
            case 2: return 'Signed';
            case 3: return 'Ready For Dispatch';
            case 4: return 'Dispatched';
            default: return 'Unknown';
        }
    }

    /**
     * Parse dispatch details to determine dispatch method and information
     */
    private function parseDispatchDetails($details, $courier = null, $trackingId = null) {
        $dispatchInfo = [
            'method' => 'Unknown',
            'details' => 'Not specified'
        ];

        // Priority 1: If courier and tracking ID are provided (from DocTrack), it's Post dispatch
        if (!empty($courier) && !empty($trackingId) && $courier !== 'Email') {
            $dispatchInfo['method'] = 'Post';
            $dispatchInfo['details'] = "Courier: $courier, Tracking ID: $trackingId";
            $dispatchInfo['courier'] = $courier;
            $dispatchInfo['tracking_id'] = $trackingId;
        }
        // Priority 2: If courier is 'Email' and trackingId contains email address
        elseif (!empty($courier) && $courier === 'Email' && !empty($trackingId) && filter_var($trackingId, FILTER_VALIDATE_EMAIL)) {
            $dispatchInfo['method'] = 'Email';
            $dispatchInfo['details'] = "Email: $trackingId";
            $dispatchInfo['email'] = $trackingId;
        }
        // Priority 3: If details contains an email address, it's Email dispatch
        elseif (!empty($details) && filter_var($details, FILTER_VALIDATE_EMAIL)) {
            $dispatchInfo['method'] = 'Email';
            $dispatchInfo['details'] = "Email: $details";
            $dispatchInfo['email'] = $details;
        }
        // Priority 4: If details is "Post" and we have courier/tracking info
        elseif (!empty($details) && strtolower($details) === 'post') {
            if (!empty($courier) && !empty($trackingId)) {
                $dispatchInfo['method'] = 'Post';
                $dispatchInfo['details'] = "Courier: $courier, Tracking ID: $trackingId";
                $dispatchInfo['courier'] = $courier;
                $dispatchInfo['tracking_id'] = $trackingId;
            } else {
                $dispatchInfo['method'] = 'Post';
                $dispatchInfo['details'] = 'Post (Courier details pending)';
            }
        }
        // Priority 5: If we have courier info but no tracking ID
        elseif (!empty($courier) && $courier !== 'Email') {
            $dispatchInfo['method'] = 'Post';
            $dispatchInfo['details'] = "Courier: $courier" . (!empty($trackingId) ? ", Tracking ID: $trackingId" : " (Tracking ID pending)");
            $dispatchInfo['courier'] = $courier;
            if (!empty($trackingId)) {
                $dispatchInfo['tracking_id'] = $trackingId;
            }
        }
        // Priority 6: If details has other content, try to determine method
        elseif (!empty($details)) {
            // Check if it might be courier info (short text without @)
            if (strlen($details) < 50 && !strpos($details, '@')) {
                $dispatchInfo['method'] = 'Post';
                $dispatchInfo['details'] = "Courier: $details";
                $dispatchInfo['courier'] = $details;
            } else {
                $dispatchInfo['method'] = 'Other';
                $dispatchInfo['details'] = $details;
            }
        }

        return $dispatchInfo;
    }

    /**
     * Get recipients for a document based on user rights and event type
     */
    private function getRecipients($refNo, $eventType) {
        $recipients = [];

        // Get document details first
        $docDetails = $this->getDocumentDetails($refNo);
        if (!$docDetails) {
            return $recipients; // Return empty array if no document found
        }

        // Always include signatory
        if ($docDetails['SignatoryEmail']) {
            $recipients[] = [
                'email' => $docDetails['SignatoryEmail'],
                'name' => $docDetails['SignatoryName'],
                'role' => 'Signatory'
            ];
        }

        // Always include creator
        if ($docDetails['CreatorEmail']) {
            $recipients[] = [
                'email' => $docDetails['CreatorEmail'],
                'name' => $docDetails['CreatorName'],
                'role' => 'Creator'
            ];
        }

        // Get users with rights to this document
        $rightsQuery = "
            SELECT DISTINCT u.email, u.name, ur.rights
            FROM UserRights ur
            JOIN users u ON ur.user_id = u.ID
            WHERE ur.refno = ? AND u.email IS NOT NULL AND u.email != ''
        ";

        $stmt = $this->pdo->prepare($rightsQuery);
        $stmt->execute([$refNo]);
        $rightsUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($rightsUsers as $user) {
            $role = $this->getRoleFromRights($user['rights']);
            $recipients[] = [
                'email' => $user['email'],
                'name' => $user['name'],
                'role' => $role
            ];
        }

        // Remove duplicates based on email
        $uniqueRecipients = [];
        $seenEmails = [];

        foreach ($recipients as $recipient) {
            if (!in_array($recipient['email'], $seenEmails)) {
                $uniqueRecipients[] = $recipient;
                $seenEmails[] = $recipient['email'];
            }
        }

        return $uniqueRecipients;
    }

    /**
     * Add test email recipient if testing is enabled
     * This method adds a configurable test email address to receive copies of all notifications
     * for testing and monitoring purposes
     */
    private function addTestEmailRecipient($recipients) {
        // Check if test email is enabled
        if (!self::ENABLE_TEST_EMAIL) {
            return $recipients;
        }

        // Check if test email is already in the recipient list to avoid duplicates
        foreach ($recipients as $recipient) {
            if ($recipient['email'] === self::TEST_EMAIL_ADDRESS) {
                return $recipients; // Test email already included
            }
        }

        // Add test email recipient
        $recipients[] = [
            'email' => self::TEST_EMAIL_ADDRESS,
            'name' => self::TEST_EMAIL_NAME,
            'role' => 'Test Monitor'
        ];

        return $recipients;
    }

    /**
     * Get all recipients including test email if enabled
     * This is the main method that combines legitimate recipients with test email
     */
    private function getAllRecipients($refNo, $eventType) {
        // Get legitimate document stakeholders
        $recipients = $this->getRecipients($refNo, $eventType);

        // Add test email if enabled (for testing purposes only)
        $recipients = $this->addTestEmailRecipient($recipients);

        return $recipients;
    }

    /**
     * Convert rights number to role name
     */
    private function getRoleFromRights($rights) {
        switch ($rights) {
            case 1: return 'Viewer';
            case 2: return 'Editor';
            case 3: return 'Reviewer';
            case 4: return 'Creator';
            case 5: return 'Signatory';
            default: return 'User';
        }
    }

    /**
     * Get status-specific date from StatusAudit table with UTC +5:00 timezone
     */
    private function getStatusSpecificDate($refNo, $status, $fallbackDate) {
        try {
            // Query StatusAudit table for the most recent status change date
            $query = "
                SELECT ChangedAt
                FROM StatusAudit
                WHERE RefNo = ? AND NewStatus = ?
                ORDER BY ChangedAt DESC
                LIMIT 1
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$refNo, $status]);
            $statusDate = $stmt->fetchColumn();

            if ($statusDate) {
                // Create DateTime object and apply UTC +5:00 timezone
                $dateTime = new DateTime($statusDate, new DateTimeZone('UTC'));
                $dateTime->setTimezone(new DateTimeZone('+05:00'));
                return $dateTime->format('Y-m-d H:i:s');
            }

            // Fall back to document creation date if no StatusAudit record exists
            return $fallbackDate;

        } catch (Exception $e) {
            error_log("Error fetching status-specific date for $refNo (status $status): " . $e->getMessage());
            // Return fallback date on error
            return $fallbackDate;
        }
    }

    /**
     * Get dynamic date based on document status and event type
     */
    private function getDynamicDate($refNo, $eventType, $docDetails) {
        $fallbackDate = $docDetails['Date']; // Document creation date

        switch ($eventType) {
            case 'draft':
                // For draft status, always show document creation date
                return $fallbackDate;

            case 'signed':
                // For signed status, show when status changed to "Signed" (Status = 2)
                return $this->getStatusSpecificDate($refNo, '2', $fallbackDate);

            case 'ready_for_dispatch':
                // For ready for dispatch status, show when status changed to "Ready For Dispatch" (Status = 3)
                return $this->getStatusSpecificDate($refNo, '3', $fallbackDate);

            case 'dispatched':
                // For dispatched status, show when status changed to "Dispatched" (Status = 4)
                return $this->getStatusSpecificDate($refNo, '4', $fallbackDate);

            default:
                // For any other event type, use document creation date
                return $fallbackDate;
        }
    }

    /**
     * Generate enhanced email template with status-specific information
     */
    private function generateEmailTemplate($docDetails, $eventType, $additionalData = []) {
        $refNo = $docDetails['RefNo'];
        $subject = $docDetails['Subject'];
        $departmentName = $docDetails['DepartmentName'] ?: $docDetails['Department'];
        $branchName = $docDetails['BranchName'] ?: $docDetails['SendTo'];
        $signatoryName = $docDetails['SignatoryName'] ?: $docDetails['Signatory'];
        $creatorName = $docDetails['CreatorName'] ?: 'System';
        $currentStatus = $this->getStatusName($docDetails['Status']);

        // Get dynamic date based on event type and status
        $date = $this->getDynamicDate($refNo, $eventType, $docDetails);

        $comment = $docDetails['Comment'];
        $addressee = $docDetails['Addresse'];

        // Parse dispatch information
        $dispatchInfo = $this->parseDispatchDetails(
            $docDetails['Details'],
            $docDetails['Courier'],
            $docDetails['TrackingId']
        );

        // Event-specific content
        $eventTitle = $this->getEventTitle($eventType);
        $eventMessage = $this->getEventMessage($eventType, $additionalData, $dispatchInfo);
        $eventColor = $this->getEventColor($eventType);

        // Remove status transition information for simplified emails

        $emailContent = "
        <!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">
        <html xmlns=\"http://www.w3.org/1999/xhtml\">
        <head>
            <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />
            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />
            <title>$eventTitle - $refNo</title>
            <!--[if mso]>
            <noscript>
                <xml>
                    <o:OfficeDocumentSettings>
                        <o:PixelsPerInch>96</o:PixelsPerInch>
                    </o:OfficeDocumentSettings>
                </xml>
            </noscript>
            <![endif]-->
        </head>
        <body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8f9fa; color: #333333; line-height: 1.6;\">
            <!-- Main Container Table -->
            <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f8f9fa; margin: 0; padding: 20px 0;\">
                <tr>
                    <td align=\"center\">
                        <!-- Email Container -->
                        <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); max-width: 600px;\">
                            <!-- Header -->
                            <tr>
                                <td style=\"background-color: #357c3c; padding: 30px; text-align: center; color: white;\">
                                    <!-- Company Logo -->
                                    <h1 style=\"margin: 0; font-size: 28px; font-weight: bold; color: white;\">Synergy Computers (Pvt) Ltd</h1>
                                    <p style=\"margin: 10px 0 0; font-size: 16px; color: white; opacity: 0.9;\">Document Management System</p>
                                </td>
                            </tr>

                            <!-- Content -->
                            <tr>
                                <td style=\"padding: 30px 25px;\">
                                    <!-- Event Badge -->
                                    <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"margin-bottom: 20px;\">
                                        <tr>
                                            <td style=\"background-color: $eventColor; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.5px;\">
                                                $eventTitle
                                            </td>
                                        </tr>
                                    </table>

                                    <!-- Document Title -->
                                    <h2 style=\"font-size: 24px; color: #2c3e50; margin: 20px 0; font-weight: bold; border-left: 4px solid #357c3c; padding-left: 15px;\">$subject</h2>

                                    <!-- Reference Number
                                    <p style=\"font-size: 18px; margin-bottom: 20px; color: #357c3c; font-weight: bold;\">Reference: $refNo</p>
                                    -->
                                    <!-- Message -->
                                    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #f8f9fa; border-left: 4px solid $eventColor; margin: 15px 0;\">
                                        <tr>
                                            <td style=\"padding: 15px; font-size: 14px; line-height: 1.5;\">
                                                $eventMessage
                                            </td>
                                        </tr>
                                    </table>

                                    <!-- Details Table - Optimized for Outlook compatibility -->
                                    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"border-collapse: collapse; margin: 20px 0; background-color: #fff; border: 1px solid #e9ecef; font-size: 14px;\">
                                        <!-- Reference Number -->
                                        <tr>
                                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; width: 35%; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Reference Number</td>
                                            <td style=\"padding: 10px 15px; color: #357c3c; font-weight: bold; border-bottom: 1px solid #e9ecef; font-size: 14px;\">$refNo</td>
                                        </tr>
                                        <!-- Department -->
                                        <tr>
                                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Department</td>
                                            <td style=\"padding: 10px 15px; color: #6c757d; border-bottom: 1px solid #e9ecef; font-size: 14px;\">$departmentName</td>
                                        </tr>
                                        <!-- Branch -->
                                        <tr>
                                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Branch</td>
                                            <td style=\"padding: 10px 15px; color: #6c757d; border-bottom: 1px solid #e9ecef; font-size: 14px;\">$branchName</td>
                                        </tr>
                                        <!-- Addressee -->
                                        <tr>
                                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Addressee</td>
                                            <td style=\"padding: 10px 15px; color: #6c757d; border-bottom: 1px solid #e9ecef; font-size: 14px;\">$addressee</td>
                                        </tr>
                                        <!-- Signatory -->
                                        <tr>
                                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Signatory</td>
                                            <td style=\"padding: 10px 15px; color: #6c757d; border-bottom: 1px solid #e9ecef; font-size: 14px;\">$signatoryName</td>
                                        </tr>
                                        <!-- Status -->
                                        <tr>
                                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Status</td>
                                            <td style=\"padding: 10px 15px; border-bottom: 1px solid #e9ecef;\">
                                                <span style=\"color: $eventColor; font-weight: bold; padding: 3px 10px; background-color: rgba(" . hexdec(substr($eventColor, 1, 2)) . "," . hexdec(substr($eventColor, 3, 2)) . "," . hexdec(substr($eventColor, 5, 2)) . ", 0.1); border-radius: 12px; font-size: 13px;\">$currentStatus</span>
                                            </td>
                                        </tr>";

        $emailContent .= "
                        <!-- Date -->
                        <tr>
                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Date</td>
                            <td style=\"padding: 10px 15px; color: #6c757d; border-bottom: 1px solid #e9ecef; font-size: 14px;\">$date</td>
                        </tr>";

        // Add optimized dispatch information for dispatched documents
        if ($docDetails['Status'] == 3 && $dispatchInfo['method'] !== 'Unknown') {
            $emailContent .= "
                        <!-- Dispatch Method -->
                        <tr style=\"background-color: #f8f9fa;\">
                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #6f42c1; border-bottom: 1px solid #e9ecef; font-size: 13px;\">📦 Dispatch Method</td>
                            <td style=\"padding: 10px 15px; font-weight: bold; color: #6f42c1; border-bottom: 1px solid #e9ecef; font-size: 14px;\">{$dispatchInfo['method']}</td>
                        </tr>";

            // Add specific dispatch information based on method
            if ($dispatchInfo['method'] === 'Post' && isset($dispatchInfo['courier'])) {
                $emailContent .= "
                        <!-- Courier -->
                        <tr style=\"background-color: #f8f9fa;\">
                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #6f42c1; border-bottom: 1px solid #e9ecef; font-size: 13px;\">🚚 Courier</td>
                            <td style=\"padding: 10px 15px; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 14px;\">{$dispatchInfo['courier']}</td>
                        </tr>";

                if (isset($dispatchInfo['tracking_id'])) {
                    $emailContent .= "
                        <!-- Tracking ID -->
                        <tr style=\"background-color: #f8f9fa;\">
                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #6f42c1; border-bottom: 1px solid #e9ecef; font-size: 13px;\">📍 Tracking ID</td>
                            <td style=\"padding: 10px 15px; font-family: monospace; font-weight: bold; color: #495057; background-color: #e9ecef; border-radius: 3px; border-bottom: 1px solid #e9ecef; font-size: 13px;\">{$dispatchInfo['tracking_id']}</td>
                        </tr>";
                }
            } elseif ($dispatchInfo['method'] === 'Email' && isset($dispatchInfo['email'])) {
                $emailContent .= "
                        <!-- Email Address -->
                        <tr style=\"background-color: #f8f9fa;\">
                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #6f42c1; border-bottom: 1px solid #e9ecef; font-size: 13px;\">📧 Email Address</td>
                            <td style=\"padding: 10px 15px; font-family: monospace; font-weight: bold; color: #495057; background-color: #e9ecef; border-radius: 3px; border-bottom: 1px solid #e9ecef; font-size: 13px;\">{$dispatchInfo['email']}</td>
                        </tr>";
            }
        }

        if (!empty($comment)) {
            $emailContent .= "
                        <!-- Comments -->
                        <tr>
                            <td style=\"padding: 10px 15px; background-color: #f8f9fa; font-weight: bold; color: #495057; border-bottom: 1px solid #e9ecef; font-size: 13px;\">Comments</td>
                            <td style=\"padding: 10px 15px; font-style: italic; color: #6c757d; border-bottom: 1px solid #e9ecef; font-size: 14px;\">$comment</td>
                        </tr>";
        }

        $emailContent .= "
                                    </table>

                                    <!-- Footer Message -->
                                    <p style=\"margin: 20px 0; color: #495057; line-height: 1.5; font-size: 14px;\">
                                        This is an automated notification from the Document Management System.
                                        <!--Please log in to the system for more details and to take any required actions.-->
                                    </p>
                                </td>
                            </tr>

                            <!-- Footer -->
                            <tr>
                                <td style=\"background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e9ecef;\">
                                    <!-- Company Logo -->
                                    <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"margin: 0 auto 15px auto;\">
                                        <tr>
                                            <td style=\"text-align: center;\">
                                                <img src=\"{$this->faviconPath}\" alt=\"Synergy Computers Logo\" style=\"width: 48px; height: 48px; border: 0; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\" />
                                            </td>
                                        </tr>
                                    </table>

                                    <p style=\"color: #6c757d; font-size: 14px; margin: 0; line-height: 1.6;\">
                                        This email was sent automatically by the Document Management System.<br>
                                        Please do not reply to this email.
                                    </p>
                                    <p style=\"color: #357c3c; font-weight: bold; margin: 10px 0 0; line-height: 1.6;\">
                                        Synergy Computers (Pvt) Ltd<br>
                                        Document Management Team
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </body>
        </html>";

        return $emailContent;
    }

    /**
     * Get event title based on event type
     */
    private function getEventTitle($eventType) {
        switch ($eventType) {
            case 'draft': return 'Document Created';
            case 'signed': return 'Document Signed';
            case 'ready_for_dispatch': return 'Document Ready For Dispatch';
            case 'dispatched': return 'Document Dispatched';
            case 'status_changed': return 'Document Status Changed';
            default: return 'Document Notification';
        }
    }

    /**
     * Get event message based on event type with dispatch information
     */
    private function getEventMessage($eventType, $additionalData = [], $dispatchInfo = []) {
        switch ($eventType) {
            case 'draft':
                return "A new document has been created. Please log in to the system & review the details below and take any necessary actions.";
            case 'signed':
                return "This document has been signed and approved by the signatory. Please log in to the system for dispatch of the document.";
            case 'ready_for_dispatch':
                return "This document has been finalized with the final version attached and is now ready for dispatch. Please log in to the system to dispatch the document.";
            case 'dispatched':
                $message = "This document has been dispatched.";

                if (!empty($dispatchInfo) && $dispatchInfo['method'] !== 'Unknown') {
                    if ($dispatchInfo['method'] === 'Post') {
                        if (isset($dispatchInfo['courier']) && isset($dispatchInfo['tracking_id'])) {
                            $message .= " Sent via <strong>{$dispatchInfo['courier']}</strong> with tracking ID <strong>{$dispatchInfo['tracking_id']}</strong>.";
                        } elseif (isset($dispatchInfo['courier'])) {
                            $message .= " Sent via <strong>{$dispatchInfo['courier']}</strong>.";
                        } else {
                            $message .= " Sent via postal service.";
                        }
                    } elseif ($dispatchInfo['method'] === 'Email') {
                        $message = "This document has been electronically dispatched.";
                        /*
                        if (isset($dispatchInfo['email'])) {
                            $message .= " Sent electronically to <strong>{$dispatchInfo['email']}</strong>.";
                        } else {
                            $message .= " Sent electronically.";
                        }
                            */
                    }
                }

                return $message;
            case 'status_changed':
                return "The document status has been updated. Please review the current information below.";
            default:
                return "This document has been updated. Please review the current status and take any necessary actions.";
        }
    }

    /**
     * Get event color based on event type
     */
    private function getEventColor($eventType) {
        switch ($eventType) {
            case 'draft': return '#28a745';      // Green - New document
            case 'signed': return '#007bff';     // Blue - Approved
            case 'ready_for_dispatch': return '#fd7e14'; // Orange - Ready for dispatch
            case 'dispatched': return '#6f42c1'; // Purple - In transit
            case 'status_changed': return '#17a2b8'; // Cyan - General change
            default: return '#6c757d';           // Gray - Default
        }
    }

    /**
     * Send notification for document creation (Draft status)
     */
    public function sendDraftStatusNotification($refNo) {
        return $this->sendNotification($refNo, 'draft');
    }

    /**
     * Send notification for document signing (Signed status)
     */
    public function sendSignedStatusNotification($refNo, $previousStatus = 'Draft') {
        $additionalData = [
            'previous_status' => $previousStatus,
            'new_status' => 'Signed'
        ];
        return $this->sendNotification($refNo, 'signed', $additionalData);
    }

    /**
     * Send notification for document ready for dispatch (Ready For Dispatch status)
     */
    public function sendReadyForDispatchNotification($refNo, $previousStatus = 'Signed') {
        $additionalData = [
            'previous_status' => $previousStatus,
            'new_status' => 'Ready For Dispatch'
        ];
        return $this->sendNotification($refNo, 'ready_for_dispatch', $additionalData);
    }

    /**
     * Send notification for document dispatch (Dispatched status)
     */
    public function sendDispatchedStatusNotification($refNo, $previousStatus = 'Ready For Dispatch') {
        $additionalData = [
            'previous_status' => $previousStatus,
            'new_status' => 'Dispatched'
        ];
        return $this->sendNotification($refNo, 'dispatched', $additionalData);
    }

    /**
     * Check if document status has actually changed
     */
    public function hasStatusChanged($refNo, $newStatus) {
        try {
            $stmt = $this->pdo->prepare("SELECT Status FROM DocDetails WHERE RefNo = ?");
            $stmt->execute([$refNo]);
            $currentStatus = $stmt->fetchColumn();

            return $currentStatus !== false && $currentStatus != $newStatus;
        } catch (Exception $e) {
            error_log("Error checking status change for document $refNo: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get current document status
     */
    public function getCurrentStatus($refNo) {
        try {
            $stmt = $this->pdo->prepare("SELECT Status FROM DocDetails WHERE RefNo = ?");
            $stmt->execute([$refNo]);
            $status = $stmt->fetchColumn();

            return $status !== false ? (int)$status : null;
        } catch (Exception $e) {
            error_log("Error getting current status for document $refNo: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate subject line with threading support
     */
    private function generateSubject($refNo, $eventType, $documentSubject) {
        $eventTitle = $this->getEventTitle($eventType);
        return "[$refNo] $eventTitle: $documentSubject";
    }

    /**
     * Send email notification
     */
    public function sendNotification($refNo, $eventType, $additionalData = []) {
        try {
            // Get document details
            $docDetails = $this->getDocumentDetails($refNo);
            if (!$docDetails) {
                throw new Exception("Document not found: $refNo");
            }

            // Get recipients (including test email if enabled)
            $recipients = $this->getAllRecipients($refNo, $eventType);
            if (empty($recipients)) {
                throw new Exception("No valid recipients found for document: $refNo. Please ensure the document has a signatory, creator, or users with document rights assigned.");
            }

            // Generate email content
            $emailContent = $this->generateEmailTemplate($docDetails, $eventType, $additionalData);
            $subject = $this->generateSubject($refNo, $eventType, $docDetails['Subject']);

            // Generate threading headers
            $this->generateThreadingHeaders($refNo, $eventType);

            // Clear previous recipients
            $this->mail->clearAddresses();
            $this->mail->clearCCs();
            $this->mail->clearBCCs();

            // Set email content
            $this->mail->Subject = $subject;
            $this->mail->Body = $emailContent;

            // Send to each recipient
            $sentCount = 0;
            $errors = [];

            foreach ($recipients as $recipient) {
                try {
                    $this->mail->clearAddresses();
                    $this->mail->addAddress($recipient['email'], $recipient['name']);

                    // Add personalized greeting if needed
                    $personalizedContent = str_replace(
                        'Dear Team,',
                        'Dear ' . $recipient['name'] . ',',
                        $emailContent
                    );
                    $this->mail->Body = $personalizedContent;

                    $this->mail->send();
                    $sentCount++;

                    // Log successful send
                    error_log("Email sent successfully to {$recipient['email']} for document $refNo ($eventType)");

                } catch (Exception $e) {
                    $errors[] = "Failed to send to {$recipient['email']}: " . $e->getMessage();
                    error_log("Email send failed to {$recipient['email']} for document $refNo: " . $e->getMessage());
                }
            }

            // Return results
            return [
                'success' => $sentCount > 0,
                'sent_count' => $sentCount,
                'total_recipients' => count($recipients),
                'errors' => $errors,
                'message' => $sentCount > 0 ?
                    "Email notifications sent successfully to $sentCount recipients" :
                    "Failed to send email notifications"
            ];

        } catch (Exception $e) {
            error_log("Email notification system error for document $refNo: " . $e->getMessage());
            return [
                'success' => false,
                'sent_count' => 0,
                'total_recipients' => 0,
                'errors' => [$e->getMessage()],
                'message' => 'Email notification system error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Legacy method - Send notification for document creation (now uses Draft status)
     * @deprecated Use sendDraftStatusNotification instead
     */
    public function sendDocumentCreatedNotification($refNo) {
        return $this->sendDraftStatusNotification($refNo);
    }

    /**
     * Legacy method - Send notification for document dispatch
     * @deprecated Use sendDispatchedStatusNotification instead
     */
    public function sendDocumentDispatchedNotification($refNo, $courier = '', $trackingId = '') {
        return $this->sendDispatchedStatusNotification($refNo);
    }

    /**
     * Legacy method - Send notification for status changes
     * @deprecated Use specific status notification methods instead
     */
    public function sendStatusChangedNotification($refNo, $oldStatus = '', $newStatus = '') {
        $additionalData = [];
        if ($oldStatus) $additionalData['previous_status'] = $oldStatus;
        if ($newStatus) $additionalData['new_status'] = $newStatus;

        return $this->sendNotification($refNo, 'status_changed', $additionalData);
    }

    /**
     * Get test email configuration status
     * Returns information about whether test email is enabled and configured
     */
    public function getTestEmailStatus() {
        return [
            'enabled' => self::ENABLE_TEST_EMAIL,
            'email' => self::ENABLE_TEST_EMAIL ? self::TEST_EMAIL_ADDRESS : null,
            'name' => self::ENABLE_TEST_EMAIL ? self::TEST_EMAIL_NAME : null,
            'message' => self::ENABLE_TEST_EMAIL ?
                'Test email is ENABLED - ' . self::TEST_EMAIL_ADDRESS . ' will receive copies of all notifications' :
                'Test email is DISABLED - no test copies will be sent'
        ];
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration($testEmail = '<EMAIL>') {
        try {
            $this->mail->clearAddresses();
            $this->mail->addAddress($testEmail);
            $this->mail->Subject = 'DMS Email Configuration Test';

            $testStatus = $this->getTestEmailStatus();
            $statusMessage = $testStatus['enabled'] ?
                "Test email monitoring is ENABLED for: " . $testStatus['email'] :
                "Test email monitoring is DISABLED";

            $this->mail->Body = "
                <h1>Email Configuration Test</h1>
                <p>If you receive this email, the email configuration is working correctly.</p>
                <hr>
                <h2>Test Email Configuration Status</h2>
                <p><strong>Status:</strong> $statusMessage</p>
                <p><em>This test was sent to: $testEmail</em></p>
            ";

            $this->mail->send();
            return [
                'success' => true,
                'message' => 'Test email sent successfully',
                'test_email_status' => $testStatus
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Test email failed: ' . $e->getMessage(),
                'test_email_status' => $this->getTestEmailStatus()
            ];
        }
    }
}

// Helper function to create EmailNotificationSystem instance
function createEmailNotificationSystem($pdo) {
    return new EmailNotificationSystem($pdo);
}

?>
