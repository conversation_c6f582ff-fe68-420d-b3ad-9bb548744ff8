<?php
session_start();
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || $_SESSION['user_type'] !== 'Admin') {
    header("Location: login.php");
    exit;
}

$login_email = $_SESSION['login_email'];
$login_id = $_SESSION['login_id'];
$pdo = new PDO("sqlite:dmsdb.db");
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Handle AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'add_department':
                $name = trim($_POST['name']);
                $code = trim($_POST['code']);

                // Check if code already exists
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM Departments WHERE DeptCode = ?");
                $stmt->execute([$code]);
                if ($stmt->fetchColumn() > 0) {
                    echo json_encode(['success' => false, 'message' => 'Department code already exists']);
                    exit;
                }

                $stmt = $pdo->prepare("INSERT INTO Departments (DeptName, DeptCode, active) VALUES (?, ?, 1)");
                $stmt->execute([$name, $code]);
                echo json_encode(['success' => true, 'message' => 'Department added successfully']);
                break;

            case 'add_branch':
                $name = trim($_POST['name']);
                $code = trim($_POST['code']);

                // Check if code already exists
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM Branch WHERE BCode = ?");
                $stmt->execute([$code]);
                if ($stmt->fetchColumn() > 0) {
                    echo json_encode(['success' => false, 'message' => 'Branch code already exists']);
                    exit;
                }

                $stmt = $pdo->prepare("INSERT INTO Branch (City, BCode, active) VALUES (?, ?, 1)");
                $stmt->execute([$name, $code]);
                echo json_encode(['success' => true, 'message' => 'Branch added successfully']);
                break;

            case 'add_address':
                $name = trim($_POST['name']);
                $code = trim($_POST['code']);

                // Check if code already exists
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM Addresses WHERE code = ?");
                $stmt->execute([$code]);
                if ($stmt->fetchColumn() > 0) {
                    echo json_encode(['success' => false, 'message' => 'Address code already exists']);
                    exit;
                }

                $stmt = $pdo->prepare("INSERT INTO Addresses (name, code, active) VALUES (?, ?, 1)");
                $stmt->execute([$name, $code]);
                echo json_encode(['success' => true, 'message' => 'Address added successfully']);
                break;

            case 'add_signatory':
                $user_id = $_POST['user_id'];

                // Check if user is already a signatory
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM Signatory WHERE user_id = ?");
                $stmt->execute([$user_id]);
                if ($stmt->fetchColumn() > 0) {
                    echo json_encode(['success' => false, 'message' => 'User is already a signatory']);
                    exit;
                }

                $stmt = $pdo->prepare("INSERT INTO Signatory (user_id, active) VALUES (?, 1)");
                $stmt->execute([$user_id]);
                echo json_encode(['success' => true, 'message' => 'Signatory added successfully']);
                break;

            case 'update_department':
                $id = $_POST['id'];
                $name = trim($_POST['name']);
                $active = $_POST['active'];

                $stmt = $pdo->prepare("UPDATE Departments SET DeptName = ?, active = ? WHERE DeptCode = ?");
                $stmt->execute([$name, $active, $id]);
                echo json_encode(['success' => true, 'message' => 'Department updated successfully']);
                break;

            case 'update_branch':
                $id = $_POST['id'];
                $name = trim($_POST['name']);
                $active = $_POST['active'];

                $stmt = $pdo->prepare("UPDATE Branch SET City = ?, active = ? WHERE BCode = ?");
                $stmt->execute([$name, $active, $id]);
                echo json_encode(['success' => true, 'message' => 'Branch updated successfully']);
                break;

            case 'update_address':
                $id = $_POST['id'];
                $name = trim($_POST['name']);
                $active = $_POST['active'];

                $stmt = $pdo->prepare("UPDATE Addresses SET name = ?, active = ? WHERE code = ?");
                $stmt->execute([$name, $active, $id]);
                echo json_encode(['success' => true, 'message' => 'Address updated successfully']);
                break;

            case 'update_signatory':
                $id = $_POST['id'];
                $active = $_POST['active'];

                $stmt = $pdo->prepare("UPDATE Signatory SET active = ? WHERE ID = ?");
                $stmt->execute([$active, $id]);
                echo json_encode(['success' => true, 'message' => 'Signatory updated successfully']);
                break;

            case 'get_data':
                $type = $_POST['type'];
                $page = isset($_POST['page']) ? (int)$_POST['page'] : 1;
                $limit = 15;
                $offset = ($page - 1) * $limit;
                $search = isset($_POST['search']) ? trim($_POST['search']) : '';

                $data = [];
                $total = 0;

                switch ($type) {
                    case 'departments':
                        $stmt = $pdo->query("SELECT DeptCode as id, DeptName as name, active FROM Departments ORDER BY DeptName");
                        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        $total = count($data);
                        break;
                    case 'branches':
                        $stmt = $pdo->query("SELECT BCode as id, City as name, active FROM Branch ORDER BY City");
                        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        $total = count($data);
                        break;
                    case 'addresses':
                        $whereClause = '';
                        $params = [];
                        if ($search) {
                            $whereClause = "WHERE name LIKE ?";
                            $params[] = "%$search%";
                        }

                        // Get total count
                        $countStmt = $pdo->prepare("SELECT COUNT(*) FROM Addresses $whereClause");
                        $countStmt->execute($params);
                        $total = $countStmt->fetchColumn();

                        // Get paginated data
                        $stmt = $pdo->prepare("SELECT code as id, name, active FROM Addresses $whereClause ORDER BY name LIMIT $limit OFFSET $offset");
                        $stmt->execute($params);
                        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        break;
                    case 'signatories':
                        $whereClause = '';
                        $params = [];
                        if ($search) {
                            $whereClause = "WHERE u.name LIKE ?";
                            $params[] = "%$search%";
                        }

                        // Get total count
                        $countStmt = $pdo->prepare("SELECT COUNT(*) FROM Signatory s JOIN Users u ON s.user_id = u.ID $whereClause");
                        $countStmt->execute($params);
                        $total = $countStmt->fetchColumn();

                        // Get paginated data with department and branch info
                        $stmt = $pdo->prepare("
                            SELECT s.ID as id, u.name, u.email, s.active,
                                   GROUP_CONCAT(DISTINCT d.DeptName) as departments,
                                   GROUP_CONCAT(DISTINCT b.City) as branches
                            FROM Signatory s
                            JOIN Users u ON s.user_id = u.ID
                            LEFT JOIN usersdept ud ON u.ID = ud.user_id
                            LEFT JOIN Departments d ON ud.dept = d.DeptCode
                            LEFT JOIN usersbranch ub ON u.ID = ub.user_id
                            LEFT JOIN Branch b ON ub.branch = b.BCode
                            $whereClause
                            GROUP BY s.ID, u.name, u.email, s.active
                            ORDER BY u.name
                            LIMIT $limit OFFSET $offset
                        ");
                        $stmt->execute($params);
                        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        break;
                }

                $totalPages = ceil($total / $limit);
                echo json_encode([
                    'success' => true,
                    'data' => $data,
                    'pagination' => [
                        'current_page' => $page,
                        'total_pages' => $totalPages,
                        'total_records' => $total,
                        'per_page' => $limit
                    ]
                ]);
                break;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
    exit;
}

// Get users for signatory dropdown with branch and department info
$users = $pdo->query("
    SELECT DISTINCT u.ID, u.name, u.email,
           GROUP_CONCAT(DISTINCT d.DeptName) as departments,
           GROUP_CONCAT(DISTINCT b.City) as branches
    FROM Users u
    LEFT JOIN usersdept ud ON u.ID = ud.user_id
    LEFT JOIN Departments d ON ud.dept = d.DeptCode
    LEFT JOIN usersbranch ub ON u.ID = ub.user_id
    LEFT JOIN Branch b ON ub.branch = b.BCode
    WHERE u.Type != 'Admin'
    GROUP BY u.ID, u.name, u.email
    ORDER BY u.name
")->fetchAll(PDO::FETCH_ASSOC);


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Fields</title>
    <link rel="icon" type="image/x-icon" href="favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f1f4f7;
            color: #333;
            line-height: 1.6;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100px;
            background: linear-gradient(135deg, #57976a, #81c281);
            color: white;
            padding: 0 30px;
            border-radius: 8px;
            position: relative;
        }

        .header-left img {
            max-height: 40px;
        }

        .header-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-email {
            font-weight: bold;
            margin-right: 30px;
        }

        .logout {
            background-color: #d2d9dc;
            color: #357c3c;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .logout:hover {
            background-color: #357c3c;
            color: white;
        }

        .container {
            max-width: 1400px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .page-header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .page-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .page-header .breadcrumb {
            color: #666;
            font-size: 14px;
        }

        .page-header .breadcrumb a {
            color: #57976a;
            text-decoration: none;
        }

        .page-header .breadcrumb a:hover {
            text-decoration: underline;
        }

        .back-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #5a6268;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            border-right: 1px solid #dee2e6;
        }

        .tab:last-child {
            border-right: none;
        }

        .tab.active {
            background: linear-gradient(135deg, #57976a, #81c281);
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #57976a;
        }

        .form-section h3 {
            color: #57976a;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #57976a;
            box-shadow: 0 0 0 2px rgba(87, 151, 106, 0.2);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #57976a;
            color: white;
        }

        .btn-primary:hover {
            background-color: #4a8359;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background-color: #e0a800;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
            display: none;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .modal-header h3 {
            color: #333;
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .pagination-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #57976a;
            color: white;
            border-color: #57976a;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #57976a;
            color: white;
            border-color: #57976a;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
            margin: 0 15px;
        }

        .search-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .data-table {
                font-size: 14px;
            }

            .data-table th,
            .data-table td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-left">
            <img src="logo-inner.png" alt="Logo" class="logo">
        </div>
        <div class="header-right">
            <div class="header-info">
                <span class="user-email"><?php echo htmlspecialchars($login_email ?? 'Guest'); ?></span>
                <button class="logout" onclick="openProfile()">Profile</button>
                <button class="logout" onclick="logout()">Logout</button>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1>Manage Fields</h1>
            <div class="breadcrumb">
                <a href="admin.php">Dashboard</a> / Manage Fields
            </div>
        </div>

        <a href="admin.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>

        <!-- Alert Messages -->
        <div id="alert-success" class="alert alert-success"></div>
        <div id="alert-danger" class="alert alert-danger"></div>

        <!-- Tabs -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('departments')">
                <i class="fas fa-building"></i> Departments
            </button>
            <button class="tab" onclick="showTab('branches')">
                <i class="fas fa-map-marker-alt"></i> Branches
            </button>
            <button class="tab" onclick="showTab('addresses')">
                <i class="fas fa-address-book"></i> Addresses
            </button>
            <button class="tab" onclick="showTab('signatories')">
                <i class="fas fa-user-tie"></i> Signatories
            </button>
        </div>

        <!-- Departments Tab -->
        <div id="departments" class="tab-content active">
            <div class="form-section">
                <h3><i class="fas fa-plus-circle"></i> Add New Department</h3>
                <form id="departmentForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="dept_name">Department Name:</label>
                            <input type="text" id="dept_name" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="dept_code">Department Code:</label>
                            <input type="text" id="dept_code" name="code" class="form-control" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Department
                    </button>
                </form>
            </div>

            <div class="form-section">
                <h3><i class="fas fa-list"></i> Existing Departments</h3>
                <table class="data-table" id="departmentsTable">
                    <thead>
                        <tr>
                            <th>Department Name</th>
                            <th>Department Code</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Branches Tab -->
        <div id="branches" class="tab-content">
            <div class="form-section">
                <h3><i class="fas fa-plus-circle"></i> Add New Branch</h3>
                <form id="branchForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="branch_name">Branch Name:</label>
                            <input type="text" id="branch_name" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="branch_code">Branch Code:</label>
                            <input type="text" id="branch_code" name="code" class="form-control" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Branch
                    </button>
                </form>
            </div>

            <div class="form-section">
                <h3><i class="fas fa-list"></i> Existing Branches</h3>
                <table class="data-table" id="branchesTable">
                    <thead>
                        <tr>
                            <th>Branch Name</th>
                            <th>Branch Code</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Addresses Tab -->
        <div id="addresses" class="tab-content">
            <div class="form-section">
                <h3><i class="fas fa-plus-circle"></i> Add New Address</h3>
                <form id="addressForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="address_name">Address Name:</label>
                            <input type="text" id="address_name" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="address_code">Address Code:</label>
                            <input type="text" id="address_code" name="code" class="form-control" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Address
                    </button>
                </form>
            </div>

            <div class="form-section">
                <h3><i class="fas fa-list"></i> Existing Addresses</h3>
                <div class="search-controls" style="margin-bottom: 20px;">
                    <input type="text" id="addressSearch" class="form-control" placeholder="Search addresses..." style="width: 300px; display: inline-block;">
                    <button type="button" class="btn btn-secondary" onclick="searchData('addresses')" style="margin-left: 10px;">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearSearch('addresses')" style="margin-left: 5px;">
                        <i class="fas fa-times"></i> Clear
                    </button>
                </div>
                <table class="data-table" id="addressesTable">
                    <thead>
                        <tr>
                            <th>Address Name</th>
                            <th>Address Code</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via JavaScript -->
                    </tbody>
                </table>
                <div id="addressesPagination" class="pagination-controls" style="margin-top: 20px; text-align: center;">
                    <!-- Pagination will be loaded via JavaScript -->
                </div>
            </div>
        </div>

        <!-- Signatories Tab -->
        <div id="signatories" class="tab-content">
            <div class="form-section">
                <h3><i class="fas fa-plus-circle"></i> Add New Signatory</h3>
                <form id="signatoryForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="signatory_user">Select User:</label>
                            <select id="signatory_user" name="user_id" class="form-control" required>
                                <option value="">Select a user...</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['ID'] ?>">
                                        <?= htmlspecialchars($user['name']) ?> (<?= htmlspecialchars($user['email']) ?>)
                                        <?php if ($user['departments'] || $user['branches']): ?>
                                            - <?= htmlspecialchars($user['departments'] ?? '') ?><?= ($user['departments'] && $user['branches']) ? ' | ' : '' ?><?= htmlspecialchars($user['branches'] ?? '') ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Signatory
                    </button>
                </form>
            </div>

            <div class="form-section">
                <h3><i class="fas fa-list"></i> Existing Signatories</h3>
                <div class="search-controls" style="margin-bottom: 20px;">
                    <input type="text" id="signatorySearch" class="form-control" placeholder="Search signatories by name..." style="width: 300px; display: inline-block;">
                    <button type="button" class="btn btn-secondary" onclick="searchData('signatories')" style="margin-left: 10px;">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearSearch('signatories')" style="margin-left: 5px;">
                        <i class="fas fa-times"></i> Clear
                    </button>
                </div>
                <table class="data-table" id="signatoriesTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Department</th>
                            <th>Branch</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via JavaScript -->
                    </tbody>
                </table>
                <div id="signatoriesPagination" class="pagination-controls" style="margin-top: 20px; text-align: center;">
                    <!-- Pagination will be loaded via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Edit Item</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form id="editForm">
                <input type="hidden" id="edit_id" name="id">
                <input type="hidden" id="edit_type" name="type">

                <div class="form-group" id="nameGroup">
                    <label for="edit_name">Name:</label>
                    <input type="text" id="edit_name" name="name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="edit_active">Status:</label>
                    <select id="edit_active" name="active" class="form-control" required>
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentTab = 'departments';
        let currentPage = 1;
        let currentSearch = '';

        // Tab switching
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');

            currentTab = tabName;
            currentPage = 1;
            currentSearch = '';

            // Clear search input
            const searchInput = document.getElementById(tabName.slice(0, -1) + 'Search');
            if (searchInput) {
                searchInput.value = '';
            }

            loadData(tabName);
        }

        // Load data for current tab
        function loadData(type, page = 1, search = '') {
            const formData = new FormData();
            formData.append('action', 'get_data');
            formData.append('type', type);
            formData.append('page', page);
            formData.append('search', search);

            fetch('addfields.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateTable(type, data.data);
                    if (data.pagination) {
                        updatePagination(type, data.pagination);
                    }
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                showAlert('danger', 'Error loading data: ' + error.message);
            });
        }

        // Populate table with data
        function populateTable(type, data) {
            const tableId = type + 'Table';
            const tbody = document.querySelector('#' + tableId + ' tbody');
            tbody.innerHTML = '';

            data.forEach(item => {
                const row = document.createElement('tr');
                const statusClass = item.active == 1 ? 'status-active' : 'status-inactive';
                const statusText = item.active == 1 ? 'Active' : 'Inactive';

                if (type === 'signatories') {
                    row.innerHTML = `
                        <td>${item.name || ''}</td>
                        <td>${item.email || ''}</td>
                        <td>${item.departments || '-'}</td>
                        <td>${item.branches || '-'}</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                        <td>
                            <button class="btn btn-warning btn-sm" onclick="editItem('${type}', '${item.id}', '${item.name}', ${item.active}, true)">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </td>
                    `;
                } else {
                    row.innerHTML = `
                        <td>${item.name}</td>
                        <td>${item.id}</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                        <td>
                            <button class="btn btn-warning btn-sm" onclick="editItem('${type}', '${item.id}', '${item.name}', ${item.active}, false)">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </td>
                    `;
                }

                tbody.appendChild(row);
            });
        }

        // Show alert message
        function showAlert(type, message) {
            const alertId = 'alert-' + type;
            const alertElement = document.getElementById(alertId);
            alertElement.textContent = message;
            alertElement.style.display = 'block';

            // Hide other alert
            const otherType = type === 'success' ? 'danger' : 'success';
            document.getElementById('alert-' + otherType).style.display = 'none';

            // Auto hide after 5 seconds
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }

        // Update pagination
        function updatePagination(type, pagination) {
            const paginationId = type + 'Pagination';
            const paginationContainer = document.getElementById(paginationId);

            if (!paginationContainer || pagination.total_pages <= 1) {
                if (paginationContainer) paginationContainer.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // Previous button
            paginationHTML += `<button class="pagination-btn" ${pagination.current_page <= 1 ? 'disabled' : ''} onclick="changePage('${type}', ${pagination.current_page - 1})">
                <i class="fas fa-chevron-left"></i> Previous
            </button>`;

            // Page numbers
            const startPage = Math.max(1, pagination.current_page - 2);
            const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `<button class="pagination-btn ${i === pagination.current_page ? 'active' : ''}" onclick="changePage('${type}', ${i})">${i}</button>`;
            }

            // Next button
            paginationHTML += `<button class="pagination-btn" ${pagination.current_page >= pagination.total_pages ? 'disabled' : ''} onclick="changePage('${type}', ${pagination.current_page + 1})">
                Next <i class="fas fa-chevron-right"></i>
            </button>`;

            // Info
            paginationHTML += `<span class="pagination-info">
                Showing ${((pagination.current_page - 1) * pagination.per_page) + 1} to ${Math.min(pagination.current_page * pagination.per_page, pagination.total_records)} of ${pagination.total_records} entries
            </span>`;

            paginationContainer.innerHTML = paginationHTML;
        }

        // Change page
        function changePage(type, page) {
            currentPage = page;
            loadData(type, page, currentSearch);
        }

        // Search functionality
        function searchData(type) {
            const searchInput = document.getElementById(type.slice(0, -1) + 'Search');
            currentSearch = searchInput ? searchInput.value : '';
            currentPage = 1;
            loadData(type, 1, currentSearch);
        }

        // Clear search
        function clearSearch(type) {
            const searchInput = document.getElementById(type.slice(0, -1) + 'Search');
            if (searchInput) {
                searchInput.value = '';
            }
            currentSearch = '';
            currentPage = 1;
            loadData(type, 1, '');
        }

        // Edit item
        function editItem(type, id, name, active, isSignatory) {
            document.getElementById('edit_id').value = id;
            document.getElementById('edit_type').value = type;
            document.getElementById('edit_name').value = name || '';
            document.getElementById('edit_active').value = active;

            // Hide name field for signatories
            const nameGroup = document.getElementById('nameGroup');
            if (isSignatory) {
                nameGroup.style.display = 'none';
                document.getElementById('modalTitle').textContent = 'Edit Signatory Status';
            } else {
                nameGroup.style.display = 'block';
                document.getElementById('modalTitle').textContent = 'Edit ' + type.slice(0, -1);
            }

            document.getElementById('editModal').style.display = 'block';
        }

        // Close modal
        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Form submissions
        document.getElementById('departmentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm('add_department', this);
        });

        document.getElementById('branchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm('add_branch', this);
        });

        document.getElementById('addressForm').addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm('add_address', this);
        });

        document.getElementById('signatoryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm('add_signatory', this);
        });

        document.getElementById('editForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const type = document.getElementById('edit_type').value;
            const singularType = type.slice(0, -1); // Remove 's' from end
            const action = 'update_' + (singularType === 'addresse' ? 'address' : singularType);
            submitForm(action, this);
        });

        // Submit form function
        function submitForm(action, form) {
            const formData = new FormData(form);
            formData.append('action', action);

            fetch('addfields.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    form.reset();
                    closeModal();
                    // Reload current tab with current page and search
                    loadData(currentTab, currentPage, currentSearch);
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                showAlert('danger', 'Error: ' + error.message);
            });
        }

        // Header functions
        function openProfile() {
            window.location.href = 'profile.php';
        }

        function logout() {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'logout.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    window.location.href = 'login.php';
                } else {
                    console.error('Failed to log out');
                }
            };
            xhr.send();
        }

        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadData('departments');

            // Add Enter key support for search inputs
            document.getElementById('addressSearch')?.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchData('addresses');
                }
            });

            document.getElementById('signatorySearch')?.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchData('signatories');
                }
            });
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
