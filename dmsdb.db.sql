BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS "DocFiles" (
	"ID"	INTEGER,
	"RefNo"	TEXT,
	"FileName"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "Branch" (
	"ID"	INTEGER,
	"City"	TEXT,
	"BCode"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "Departments" (
	"ID"	INTEGER,
	"DeptName"	TEXT,
	"DeptCode"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "Addresses" (
	"ID"	INTEGER,
	"name"	TEXT,
	"code"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "Signatory" (
	"ID"	INTEGER,
	"user_id"	INTEGER UNIQUE,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "Status" (
	"ID"	INTEGER,
	"Status"	TEXT UNIQUE,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "UserRights" (
	"ID"	INTEGER,
	"refno"	TEXT,
	"user_id"	TEXT,
	"rights"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "UsersDept" (
	"ID"	INTEGER,
	"user_id"	INTEGER,
	"dept"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "UsersBranch" (
	"ID"	INTEGER,
	"user_id"	INTEGER,
	"branch"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "Users" (
	"ID"	INTEGER,
	"name"	TEXT,
	"email"	TEXT,
	"password"	TEXT,
	"app"	TEXT,
	"Type"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "DocTrack" (
	"ID"	INTEGER,
	"RefNo"	TEXT,
	"Status"	INTEGER,
	"Courier"	TEXT,
	"TrackingId"	TEXT,
	"UpdatedBy"	Text,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "StatusAudit" (
	"id"	INTEGER,
	"RefNo"	VARCHAR(50),
	"OldStatus"	TEXT,
	"NewStatus"	TEXT,
	"ChangedBy"	INT,
	"ChangedAt"	TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY("id" AUTOINCREMENT)
);
CREATE TABLE IF NOT EXISTS "DocDetails" (
	"ID"	INTEGER,
	"RefNo"	TEXT,
	"Department"	TEXT,
	"SendTo"	TEXT,
	"Addresse"	TEXT,
	"Signatory"	TEXT,
	"Date"	DATETIME,
	"Subject"	TEXT,
	"Comment"	TEXT,
	"Status"	TEXT,
	"UpdatedBy"	INT,
	"Details"	TEXT,
	PRIMARY KEY("ID" AUTOINCREMENT)
);
CREATE TRIGGER after_status_update
AFTER UPDATE ON DocDetails
FOR EACH ROW
WHEN OLD.Status <> NEW.Status
BEGIN
    INSERT INTO StatusAudit (RefNo, OldStatus, NewStatus, ChangedBy, ChangedAt)
    VALUES (NEW.RefNo, OLD.Status, NEW.Status, NEW.UpdatedBy, CURRENT_TIMESTAMP);
END;
COMMIT;
