<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: login.php");
    exit;
}

$login_email = $_SESSION['login_email'];
$login_id = $_SESSION['login_id'];
$user_type = $_SESSION['user_type'];

// Rights checking function (same as edit.php)
function check_rights($pdo, $refno, $login_id) {
    // Prepare the SQL query to get max rights
    $rights_query = "SELECT
    CASE
        WHEN Status != 6 THEN 0  -- If Status is not 1, set max_rights to 0
        ELSE max_rights         -- Else, use the calculated max_rights
    END AS max_rights
FROM (
    SELECT
        CASE
            WHEN s.user_id = :login_id AND d.Signatory = s.id THEN 5  -- Highest priority: Signatory
            --WHEN u.id IS NOT NULL AND u.type = 'Admin' THEN 3        -- Second priority: Admin
            ELSE COALESCE(MAX(ur.rights), 0)                     -- Lowest priority: UserRights table
        END AS max_rights,
        (SELECT d.Status FROM DocDetails d WHERE d.RefNo = :refno) AS Status
    FROM users u
    LEFT JOIN userrights ur ON ur.user_id = u.id AND ur.refno = :refno
    LEFT JOIN signatory s ON s.user_id = u.id
    LEFT JOIN DocDetails d ON d.Signatory = s.id AND d.RefNo = :refno
    WHERE u.id = :login_id
) AS subquery";
    
    $stmt = $pdo->prepare($rights_query);
    $stmt->bindParam(':refno', $refno, PDO::PARAM_STR);
    $stmt->bindParam(':login_id', $login_id, PDO::PARAM_INT);
    $stmt->execute();
    $rights = $stmt->fetch(PDO::FETCH_ASSOC);
    return $rights['max_rights'] ?? null;
}

date_default_timezone_set('Asia/Karachi');
$dbFile = 'dmsdb.db';
$pdo = new PDO("sqlite:$dbFile");
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Get RefNo from URL parameter
$refNo = isset($_GET['refno']) ? $_GET['refno'] : '';

if (empty($refNo)) {
    header("Location: " . ($user_type === 'Admin' ? 'admin.php' : 'index.php'));
    exit;
}

// Check user rights for this document
$user_rights = check_rights($pdo, $refNo, $login_id);
echo $user_rights;
if ($user_rights === null || $user_rights < 1) {
    // User has no rights to view this document
    header("Location: " . ($user_type === 'Admin' ? 'admin.php' : 'index.php') . "?error=access_denied");
    exit;
}

// Fetch comprehensive document details with all joins
$docQuery = "
    SELECT d.*, 
           dept.DeptName as DepartmentName,
           dept.DeptCode as DepartmentCode,
           branch.City as BranchName,
           branch.BCode as BranchCode,
           addr.name as AddresseeName,
           addr.code as AddresseeCode,
           sig_user.name as SignatoryName,
           sig_user.email as SignatoryEmail,
           creator.name as CreatorName,
           creator.email as CreatorEmail,
           updater.name as UpdatedByName,
           updater.email as UpdatedByEmail,
           status.Status as StatusName
    FROM DocDetails d
    LEFT JOIN Departments dept ON d.Department = dept.DeptCode
    LEFT JOIN Branch branch ON d.SendTo = branch.BCode
    LEFT JOIN Addresses addr ON d.Addresse = addr.code
    LEFT JOIN Signatory sig ON d.Signatory = sig.ID
    LEFT JOIN Users sig_user ON sig.user_id = sig_user.ID
    LEFT JOIN UserRights ur ON d.RefNo = ur.refno AND ur.rights = 4
    LEFT JOIN Users creator ON ur.user_id = creator.ID
    LEFT JOIN Users updater ON d.UpdatedBy = updater.ID
    LEFT JOIN Status status ON d.Status = status.ID
    WHERE d.RefNo = :refno
";

$docStmt = $pdo->prepare($docQuery);
$docStmt->execute([':refno' => $refNo]);
$document = $docStmt->fetch(PDO::FETCH_ASSOC);

if (!$document) {
    header("Location: " . ($user_type === 'Admin' ? 'admin.php' : 'index.php') . "?error=document_not_found");
    exit;
}

// Fetch status audit history
$statusAuditQuery = "
    SELECT sa.*, 
           u.name as ChangedByName,
           u.email as ChangedByEmail,
           old_status.Status as OldStatusName,
           new_status.Status as NewStatusName
    FROM StatusAudit sa
    LEFT JOIN Users u ON sa.ChangedBy = u.ID
    LEFT JOIN Status old_status ON sa.OldStatus = old_status.ID
    LEFT JOIN Status new_status ON sa.NewStatus = new_status.ID
    WHERE sa.RefNo = :refno
    ORDER BY sa.ChangedAt ASC
";

$statusAuditStmt = $pdo->prepare($statusAuditQuery);
$statusAuditStmt->execute([':refno' => $refNo]);
$statusHistory = $statusAuditStmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch document tracking information
$trackQuery = "
    SELECT dt.*, 
           u.name as UpdatedByName,
           u.email as UpdatedByEmail,
           status.Status as StatusName
    FROM DocTrack dt
    LEFT JOIN Users u ON dt.UpdatedBy = u.ID
    LEFT JOIN Status status ON dt.Status = status.ID
    WHERE dt.RefNo = :refno
    ORDER BY dt.ID DESC
";

$trackStmt = $pdo->prepare($trackQuery);
$trackStmt->execute([':refno' => $refNo]);
$trackingInfo = $trackStmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch all user rights for this document
$rightsQuery = "
    SELECT ur.*, 
           u.name as UserName,
           u.email as UserEmail,
           u.Type as UserType
    FROM UserRights ur
    LEFT JOIN Users u ON ur.user_id = u.ID
    WHERE ur.refno = :refno
    ORDER BY ur.rights DESC, u.name ASC
";

$rightsStmt = $pdo->prepare($rightsQuery);
$rightsStmt->execute([':refno' => $refNo]);
$userRightsList = $rightsStmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch document files
$filesQuery = "SELECT * FROM DocFiles WHERE RefNo = :refno ORDER BY ID ASC";
$filesStmt = $pdo->prepare($filesQuery);
$filesStmt->execute([':refno' => $refNo]);
$documentFiles = $filesStmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch all departments, branches, addresses, and signatories for display
$deptQuery = "SELECT * FROM Departments ORDER BY DeptName";
$deptStmt = $pdo->query($deptQuery);
$departments = $deptStmt->fetchAll(PDO::FETCH_ASSOC);

$branchQuery = "SELECT * FROM Branch ORDER BY City";
$branchStmt = $pdo->query($branchQuery);
$branches = $branchStmt->fetchAll(PDO::FETCH_ASSOC);

$addressQuery = "SELECT * FROM Addresses ORDER BY name";
$addressStmt = $pdo->query($addressQuery);
$addresses = $addressStmt->fetchAll(PDO::FETCH_ASSOC);

$signatoryQuery = "
    SELECT s.ID, u.name, u.email 
    FROM Signatory s 
    LEFT JOIN Users u ON s.user_id = u.ID 
    ORDER BY u.name
";
$signatoryStmt = $pdo->query($signatoryQuery);
$signatories = $signatoryStmt->fetchAll(PDO::FETCH_ASSOC);

// Function to get status name by ID
function getStatusName($statusId) {
    switch ($statusId) {
        case 1: return 'Draft';
        case 2: return 'Signed';
        case 3: return 'Ready For Dispatch';
        case 4: return 'Dispatched';
        default: return 'Unknown';
    }
}

// Function to get rights description
function getRightsDescription($rights) {
    switch ($rights) {
        case 1: return 'View Only';
        case 2: return 'Edit';
        case 3: return 'Sign';
        case 4: return 'Creator';
        case 5: return 'Admin';
        default: return 'Unknown';
    }
}

// Function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Function to get file icon based on extension
function getFileIcon($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    switch ($extension) {
        case 'pdf': return 'fas fa-file-pdf';
        case 'doc':
        case 'docx': return 'fas fa-file-word';
        case 'xls':
        case 'xlsx': return 'fas fa-file-excel';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif': return 'fas fa-file-image';
        case 'zip':
        case 'rar': return 'fas fa-file-archive';
        default: return 'fas fa-file';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Details - <?php echo htmlspecialchars($refNo); ?></title>
    <link rel="icon" type="image/x-icon" href="favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f1f4f7;
            color: #333;
            line-height: 1.6;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100px;
            background: linear-gradient(135deg, #57976a, #81c281);
            color: white;
            padding: 0 30px;
            border-radius: 8px;
            position: relative;
        }
        
        .header-left img {
            max-height: 40px;
        }
        
        .header-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .header-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-email {
            font-weight: bold;
            margin-right: 30px;
        }
        
        .logout {
            background-color: #d2d9dc;
            color: #357c3c;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .logout:hover {
            background-color: #357c3c;
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .page-header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .page-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .page-header .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .page-header .breadcrumb a {
            color: #57976a;
            text-decoration: none;
        }
        
        .page-header .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .back-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            background-color: #5a6268;
        }
        
        .form-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(135deg, #57976a, #81c281);
            color: white;
            padding: 25px 30px;
            font-size: 20px;
            font-weight: 500;
        }
        
        .form-content {
            padding: 30px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            background-color: #f8f9fa;
            color: #666;
            cursor: not-allowed;
        }
        
        .form-control:disabled {
            background-color: #f8f9fa;
            color: #666;
        }
        
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-draft { background-color: #fff3cd; color: #856404; }
        .status-signed { background-color: #d1ecf1; color: #0c5460; }
        .status-ready { background-color: #ffeaa7; color: #856404; }
        .status-dispatched { background-color: #d4edda; color: #155724; }
        
        .audit-section {
            margin-top: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .audit-header {
            background: linear-gradient(135deg, #6c757d, #adb5bd);
            color: white;
            padding: 20px 30px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .audit-content {
            padding: 25px 30px;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ddd;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 25px;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 8px;
            border-left: 4px solid #57976a;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #57976a;
            border: 3px solid white;
        }
        
        .timeline-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .timeline-content {
            font-size: 14px;
            color: #333;
        }
        
        .table-responsive {
            overflow-x: auto;
            margin-top: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        table th, table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 14px;
        }
        
        table th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #666;
        }
        
        .file-tree {
            list-style: none;
            padding: 0;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .file-item:hover {
            background-color: #f8f9fa;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-icon {
            margin-right: 15px;
            font-size: 20px;
            color: #666;
            width: 25px;
            text-align: center;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        
        .file-meta {
            font-size: 12px;
            color: #666;
        }
        
        .file-actions {
            margin-left: 15px;
        }
        
        .download-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        
        .download-btn:hover {
            background-color: #45a049;
        }
        
        @media (max-width: 768px) {
            header {
                flex-direction: column;
                height: auto;
                padding: 20px;
            }
            
            .header-right {
                margin-top: 20px;
                align-items: center;
            }
            
            .header-info {
                flex-direction: column;
                gap: 10px;
            }
            
            .user-email {
                margin-right: 0;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-left">
            <img src="logo-inner.png" alt="Logo" class="logo">
        </div>
        <div class="header-right">
            <div class="header-info">
                <span class="user-email"><?php echo htmlspecialchars($login_email ?? 'Guest'); ?></span>
                <button class="logout" onclick="openProfile()">Profile</button>
                <button class="logout" onclick="logout()">Logout</button>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1>Document Details (Read-Only)</h1>
            <div class="breadcrumb">
                <a href="<?php echo $user_type === 'Admin' ? 'admin.php' : 'index.php'; ?>">Dashboard</a>
                / Document Details / <?php echo htmlspecialchars($refNo); ?>
            </div>
        </div>

        <a href="<?php echo $user_type === 'Admin' ? 'admin.php' : 'index.php'; ?>" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>

        <!-- Main Document Form -->
        <div class="form-container">
            <div class="form-header">
                <i class="fas fa-file-alt"></i> Document Information
            </div>
            <div class="form-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="refno">Reference Number:</label>
                        <input type="text" id="refno" class="form-control" value="<?php echo htmlspecialchars($document['RefNo']); ?>" disabled>
                    </div>

                    <div class="form-group">
                        <label for="status">Status:</label>
                        <div>
                            <span class="status-badge status-<?php echo strtolower(str_replace(' ', '', $document['StatusName'])); ?>">
                                <?php echo htmlspecialchars($document['StatusName']); ?>
                            </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="department">Department:</label>
                        <input type="text" id="department" class="form-control"
                               value="<?php echo htmlspecialchars($document['DepartmentName'] ?? 'Not Assigned'); ?>" disabled>
                    </div>

                    <div class="form-group">
                        <label for="sendto">Send To (Branch):</label>
                        <input type="text" id="sendto" class="form-control"
                               value="<?php echo htmlspecialchars($document['BranchName'] ?? 'Not Assigned'); ?>" disabled>
                    </div>

                    <div class="form-group">
                        <label for="addressee">Addressee:</label>
                        <input type="text" id="addressee" class="form-control"
                               value="<?php echo htmlspecialchars($document['AddresseeName'] ?? 'Not Assigned'); ?>" disabled>
                    </div>

                    <div class="form-group">
                        <label for="signatory">Signatory:</label>
                        <input type="text" id="signatory" class="form-control"
                               value="<?php echo htmlspecialchars($document['SignatoryName'] ?? 'Not Assigned'); ?>" disabled>
                    </div>

                    <div class="form-group">
                        <label for="date">Date:</label>
                        <input type="text" id="date" class="form-control"
                               value="<?php echo date('Y-m-d H:i', strtotime($document['Date'])); ?>" disabled>
                    </div>

                    <div class="form-group">
                        <label for="creator">Creator:</label>
                        <input type="text" id="creator" class="form-control"
                               value="<?php echo htmlspecialchars($document['CreatorName'] ?? 'Not Assigned'); ?>" disabled>
                    </div>

                    <div class="form-group">
                        <label for="updated_by">Last Updated By:</label>
                        <input type="text" id="updated_by" class="form-control"
                               value="<?php echo htmlspecialchars($document['UpdatedByName'] ?? 'Not Available'); ?>" disabled>
                    </div>
                </div>

                <div class="form-group">
                    <label for="subject">Subject:</label>
                    <input type="text" id="subject" class="form-control"
                           value="<?php echo htmlspecialchars($document['Subject']); ?>" disabled>
                </div>

                <div class="form-group">
                    <label for="comment">Comments:</label>
                    <textarea id="comment" class="form-control" disabled><?php echo htmlspecialchars($document['Comment']); ?></textarea>
                </div>

                <?php if (!empty($document['Details'])): ?>
                <div class="form-group">
                    <label for="details">Dispatch Details:</label>
                    <textarea id="details" class="form-control" disabled><?php echo htmlspecialchars($document['Details']); ?></textarea>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Document Files Section -->
        <?php if (count($documentFiles) > 0): ?>
        <div class="audit-section">
            <div class="audit-header">
                <i class="fas fa-folder-open"></i> Document Files
            </div>
            <div class="audit-content">
                <ul class="file-tree">
                    <?php foreach ($documentFiles as $file): ?>
                        <?php
                        $filePath = "uploads/" . $refNo . "/" . $file['FileName'];
                        $fileExists = file_exists($filePath);
                        $fileSize = $fileExists ? filesize($filePath) : 0;

                        // Format final documents with "Final | " prefix for display
                        $displayName = $file['FileName'];
                        if (strpos($file['FileName'], 'final/') === 0) {
                            $displayName = 'Final | ' . substr($file['FileName'], 6);
                        }
                        ?>
                        <li class="file-item">
                            <div class="file-icon">
                                <i class="<?php echo getFileIcon($file['FileName']); ?>"></i>
                            </div>
                            <div class="file-info">
                                <div class="file-name"><?php echo htmlspecialchars($displayName); ?></div>
                                <div class="file-meta">
                                    <?php if ($fileExists): ?>
                                        Size: <?php echo formatFileSize($fileSize); ?>
                                    <?php else: ?>
                                        <span style="color: #dc3545;">File not found</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="file-actions">
                                <?php if ($fileExists): ?>
                                    <a href="<?php echo $filePath; ?>" class="download-btn" download>
                                        <i class="fas fa-download"></i> Download
                                    </a>
                                <?php endif; ?>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        <?php endif; ?>

        <!-- Status History Section -->
        <div class="audit-section">
            <div class="audit-header">
                <i class="fas fa-history"></i> Status Change History
            </div>
            <div class="audit-content">
                <?php if (count($statusHistory) > 0): ?>
                    <div class="timeline">
                        <?php foreach ($statusHistory as $history): ?>
                            <div class="timeline-item">
                                <div class="timeline-date">
                                    <?php echo date('M d, Y H:i:s', strtotime($history['ChangedAt'])); ?>
                                </div>
                                <div class="timeline-content">
                                    <strong>Status changed from "<?php echo htmlspecialchars($history['OldStatusName']); ?>" to "<?php echo htmlspecialchars($history['NewStatusName']); ?>"</strong>
                                    <?php if ($history['ChangedByName']): ?>
                                        <br>Changed by: <?php echo htmlspecialchars($history['ChangedByName']); ?> (<?php echo htmlspecialchars($history['ChangedByEmail']); ?>)
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p>No status changes recorded.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Document Tracking Section -->
        <?php if (count($trackingInfo) > 0): ?>
        <div class="audit-section">
            <div class="audit-header">
                <i class="fas fa-shipping-fast"></i> Document Tracking Information
            </div>
            <div class="audit-content">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Courier</th>
                                <th>Tracking ID</th>
                                <th>Updated By</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($trackingInfo as $track): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($track['StatusName'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($track['Courier'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($track['TrackingId'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($track['UpdatedByName'] ?? 'N/A'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- User Access Rights Section -->
        <div class="audit-section">
            <div class="audit-header">
                <i class="fas fa-users"></i> User Access Rights
            </div>
            <div class="audit-content">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>User Type</th>
                                <th>Access Level</th>
                                <th>Rights Code</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($userRightsList as $right): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($right['UserName'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($right['UserEmail'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($right['UserType'] ?? 'N/A'); ?></td>
                                    <td><?php echo getRightsDescription($right['rights']); ?></td>
                                    <td><?php echo $right['rights']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openProfile() {
            window.location.href = 'profile.php';
        }

        function logout() {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'logout.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    window.location.href = 'login.php';
                } else {
                    console.error('Failed to log out');
                }
            };
            xhr.send();
        }
    </script>
</body>
</html>
