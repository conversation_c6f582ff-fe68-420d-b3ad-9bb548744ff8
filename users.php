<?php
// Database connection
session_start();
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || $_SESSION['user_type'] !== 'Admin') {
    header("Location: login.php");
    exit;
}
$login_email = $_SESSION['login_email'];
$login_id = $_SESSION['login_id'];
$pdo = new PDO("sqlite:dmsdb.db");
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Pagination logic
$itemsPerPage = 15;  // Number of items per page
$currentPage = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;  // Get current page from URL, default to 1, ensure minimum 1

// Calculate the starting index for the current page
$startIndex = ($currentPage - 1) * $itemsPerPage;

// Filter parameters
$filters = [
    'name' => $_GET['name'] ?? '',
    'email' => $_GET['email'] ?? '',
    'department' => $_GET['department'] ?? '',
    'branch' => $_GET['branch'] ?? '',
    'type' => $_GET['type'] ?? '',
    'app' => $_GET['app'] ?? ''
];

$where = [];
$params = [];

// Column mapping for filters
$columnMap = [
    'name' => 'u.name',
    'email' => 'u.email',
    'department' => 'd.DeptName',
    'branch' => 'b.City',
    'type' => 'u.Type',
    'app' => 'u.app'
];

// Build WHERE clause for filters
foreach ($filters as $key => $value) {
    if (!empty($value) && isset($columnMap[$key])) {
        if ($key === 'app') {
            // Special handling for app status (Approved/Pending)
            if (strtolower($value) === 'approved' || $value === '1') {
                $where[] = $columnMap[$key] . " = :$key";
                $params[":$key"] = 1;
            } elseif (strtolower($value) === 'pending' || $value === '0') {
                $where[] = $columnMap[$key] . " = :$key";
                $params[":$key"] = 0;
            }
        } else {
            $where[] = "LOWER(" . $columnMap[$key] . ") LIKE :$key";
            $params[":$key"] = '%' . strtolower($value) . '%';
        }
    }
}

$whereClause = count($where) > 0 ? 'WHERE ' . implode(' AND ', $where) : '';

// Fetch users with pagination and filters
$query = "SELECT u.id AS user_id, u.name, u.email, u.app, u.Type,
                 COALESCE(b.City, 'N/A') as branch,
                 COALESCE(d.DeptName, 'N/A') as dept
          FROM Users u
          LEFT JOIN UsersBranch ub ON u.id = ub.user_id
          LEFT JOIN branch b ON ub.branch = b.BCode
          LEFT JOIN UsersDept ud ON u.id = ud.user_id
          LEFT JOIN Departments d ON ud.dept = d.DeptCode
          $whereClause
          ORDER BY u.name ASC
          LIMIT :limit OFFSET :offset";

$stmt = $pdo->prepare($query);
$stmt->bindValue(':limit', $itemsPerPage, PDO::PARAM_INT);
$stmt->bindValue(':offset', $startIndex, PDO::PARAM_INT);
foreach ($params as $key => $val) {
    $stmt->bindValue($key, $val);
}
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch the total number of records to calculate total pages
// Use a more efficient count query
if (empty($where)) {
    // If no filters, use simple count
    $totalCountQuery = "SELECT COUNT(*) AS total FROM Users";
    $totalStmt = $pdo->prepare($totalCountQuery);
    $totalStmt->execute();
} else {
    // If filters are applied, use the same joins as main query
    $totalCountQuery = "SELECT COUNT(DISTINCT u.id) AS total
                        FROM Users u
                        LEFT JOIN UsersBranch ub ON u.id = ub.user_id
                        LEFT JOIN branch b ON ub.branch = b.BCode
                        LEFT JOIN UsersDept ud ON u.id = ud.user_id
                        LEFT JOIN Departments d ON ud.dept = d.DeptCode
                        $whereClause";

    $totalStmt = $pdo->prepare($totalCountQuery);
    foreach ($params as $key => $val) {
        $totalStmt->bindValue($key, $val);
    }
    $totalStmt->execute();
}

$totalItems = $totalStmt->fetch(PDO::FETCH_ASSOC)['total'];
$totalPages = ceil($totalItems / $itemsPerPage);

if ($totalPages < 1) {
    $totalPages = 1;
}

// Ensure the current page is within valid range
$currentPage = max(1, min($currentPage, $totalPages));

// Fetch user data for editing
$userData = null;
if (isset($_GET['user_id']) && filter_var($_GET['user_id'], FILTER_VALIDATE_INT)) {
    $userId = $_GET['user_id'];
    $stmt = $pdo->prepare("SELECT ID, name, email, Type FROM users WHERE id = :user_id");
    $stmt->execute([':user_id' => $userId]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$userData) {
        $userData = ['error' => 'User not found'];
    }
}

// Handle form submission for editing user
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_user'])) {
    $user_id = $_POST['user_id'];
    $branch = $_POST['branch'];
    $dept = $_POST['dept'];
    $type = $_POST['type'];
    $password = $_POST['password'];

    try {
        // Update user information
        $updateQuery = "UPDATE Users SET Type = :type" . ($password ? ", password = :password" : "") . " WHERE id = :user_id";
        $stmt = $pdo->prepare($updateQuery);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':type', $type);
        if ($password) $stmt->bindParam(':password', $password);
        $stmt->execute();

        // Update user branch
        $pdo->prepare("DELETE FROM UsersBranch WHERE user_id = :user_id")->execute([':user_id' => $user_id]);
        $pdo->prepare("INSERT INTO UsersBranch (user_id, branch) VALUES (:user_id, :branch)")->execute([':user_id' => $user_id, ':branch' => $branch]);

        // Update user department
        $pdo->prepare("DELETE FROM UsersDept WHERE user_id = :user_id")->execute([':user_id' => $user_id]);
        $pdo->prepare("INSERT INTO UsersDept (user_id, dept) VALUES (:user_id, :dept)")->execute([':user_id' => $user_id, ':dept' => $dept]);

        echo "<script>alert('User updated successfully!'); window.location.href = window.location.href;</script>";
    } catch (PDOException $e) {
        echo "<script>alert('Error updating user: " . $e->getMessage() . "');</script>";
    }
}

// Add new user functionality (optional)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_user'])) {
    // Add user logic as per your initial code
}
?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link rel="icon" type="image/x-icon" href="favicon.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
/* General reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fc;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-height: 100vh;
    color: #333;
    padding: 20px 0;
}

/* Header Styling */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 105%;
    height: 100px;
    padding: 20px 5%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    margin-bottom: 20px;
}

/* Logo on the left */
.header-left img {
    max-height: 40px;
}

/* Right-side email and buttons */
.header-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 15px;
}
.logout,
.back-btn {
    width: 100px; /* Ensures both buttons are the same width */
    text-align: center;
}

.back-btn {
    margin-top: 10px; /* Adds spacing below the logout button */
}

/* Email styling */
.user-email {
    font-weight: bold;
}

/* Container for Logout & Back buttons (Stacked in a column) */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 10px; /* Space between buttons */
}

/* Logout button */
.logout {
    background-color: #d2d9dc;
    color: #357c3c;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.logout:hover {
    background-color: #357c3c;
    color: white;
}

/* Back button */
.back-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.back-btn:hover {
    background-color: #5a6268;
}

/* Enhanced Container Styling */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    border: 1px solid rgba(53, 124, 60, 0.1);
}

.container h2 {
    background: linear-gradient(135deg, #357c3c, #4a9d4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
}

.container h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(135deg, #357c3c, #4a9d4e);
    border-radius: 2px;
}

.datatable-container {
    padding: 25px;
    background-color: #fff;
    margin: 30px auto;
    border-radius: 12px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    overflow-x: auto;
    border: 1px solid rgba(53, 124, 60, 0.05);
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table th,
table td {
    padding: 15px;
    border: 1px solid #ddd;
    text-align: left;
    font-size: 14px;
    vertical-align: middle;
}

table th {
    background-color: #d2d9dc;
    font-weight: bold;
    position: relative;
}

/* Filter Input Fields in Table Headers */
table th div {
    margin-top: 5px;
}

.filter-input {
    padding: 8px;
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    background-color: #f9f9f9;
    box-sizing: border-box;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.filter-input:focus {
    border-color: #357c3c;
    outline: none;
}

/* Pagination Styles */
.pagination {
    margin-top: 20px;
    text-align: center;
    font-family: Arial, sans-serif;
}

.pagination-button {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 3px;
    background-color: #357c3c;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
}

.pagination-button:hover {
    background-color: rgb(55, 104, 65);
}

.pagination-button.disabled {
    background-color: #ccc;
    color: #666;
    pointer-events: none;
    cursor: not-allowed;
}

.pagination-button.active {
    background-color: rgb(78, 84, 90);
    font-weight: bold;
}

/* Loading indicator styles */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #357c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.filter-loading {
    opacity: 0.7;
}

/* Enhanced Button Styling */
.clear-filters-btn {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 15px;
    margin-left: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.clear-filters-btn:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.clear-filters-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(108, 117, 125, 0.2);
}

/* Enhanced Data Cells */
table td {
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

table td:hover {
    background-color: #f1f1f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Enhanced table styling */
table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

table th {
    background: linear-gradient(135deg, #357c3c, #4a9d4e);
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Action link styling */
table td a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #357c3c, #4a9d4e);
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(53, 124, 60, 0.3);
}

table td a:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(53, 124, 60, 0.4);
}

/* Enhanced Add User Button */
.btn-add {
    display: inline-block;
    padding: 14px 28px;
    background: linear-gradient(135deg, #357c3c, #4a9d4e);
    color: #fff;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
    margin-right: 15px;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(53, 124, 60, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-add::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-add:hover::before {
    left: 100%;
}

.btn-add:hover {
    background: linear-gradient(135deg, #2c6b2e, #357c3c);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(53, 124, 60, 0.4);
}

.btn-add:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(53, 124, 60, 0.3);
}
/* Enhanced Modal Styles */
#editModal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

#editModal .modal-content {
    background-color: #ffffff;
    margin: 3% auto;
    padding: 0;
    border: none;
    width: 90%;
    max-width: 700px;
    min-height: 500px;
    max-height: 90vh;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    position: relative;
    animation: modalSlideIn 0.4s ease-out;
}

#editModal h3 {
    background: linear-gradient(135deg, #357c3c, #4a9d4e);
    color: white;
    margin: 0;
    padding: 25px 35px;
    font-size: 22px;
    font-weight: 600;
    border-radius: 16px 16px 0 0;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#editModal form {
    padding: 35px;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
    scroll-behavior: smooth;
}

#editModal form::-webkit-scrollbar {
    width: 8px;
}

#editModal form::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#editModal form::-webkit-scrollbar-thumb {
    background: #357c3c;
    border-radius: 4px;
}

#editModal form::-webkit-scrollbar-thumb:hover {
    background: #2c6b2e;
}

#editModal .form-group {
    margin-bottom: 25px;
}

#editModal .form-group label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

#editModal .form-group input,
#editModal .form-group select {
    width: 100%;
    padding: 14px 18px;
    border-radius: 10px;
    border: 2px solid #e1e5e9;
    font-size: 16px;
    outline: none;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

#editModal .form-group input:focus,
#editModal .form-group select:focus {
    border-color: #357c3c;
    background-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(53, 124, 60, 0.1);
    transform: translateY(-1px);
}

#editModal .password-group {
    position: relative;
}

#editModal .password-wrapper {
    position: relative;
}

#editModal .toggle-password {
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #357c3c;
    font-size: 1.3rem;
    transition: all 0.3s ease;
    padding: 5px;
}

#editModal .toggle-password:hover {
    color: #2a6330;
    transform: translateY(-50%) scale(1.1);
}

#editModal .btn-add {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #357c3c, #4a9d4e);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 10px;
    box-shadow: 0 4px 15px rgba(53, 124, 60, 0.3);
}

#editModal .btn-add:hover {
    background: linear-gradient(135deg, #2c6b2e, #357c3c);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(53, 124, 60, 0.4);
}

#editModal .btn-add:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(53, 124, 60, 0.3);
}
/* Enhanced Close Button */
.close-btn {
    position: absolute;
    top: 20px;
    right: 25px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
}

.close-btn:hover,
.close-btn:focus {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
    text-decoration: none;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    #editModal .modal-content {
        width: 95%;
        max-width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }

    #editModal h3 {
        padding: 20px 25px;
        font-size: 20px;
    }

    #editModal form {
        padding: 25px 20px;
    }

    .datatable-container {
        margin: 10px;
        padding: 15px;
        overflow-x: auto;
    }

    table th,
    table td {
        padding: 8px;
        font-size: 12px;
        min-width: 80px;
    }

    .filter-input {
        padding: 6px;
        font-size: 12px;
        min-width: 60px;
    }

    .btn-add {
        font-size: 1rem;
        padding: 12px 24px;
        margin-right: 10px;
    }

    .clear-filters-btn {
        font-size: 12px;
        padding: 8px 16px;
        margin-left: 5px;
    }

    .pagination {
        font-size: 12px;
    }

    .pagination-button {
        padding: 4px 8px;
        margin: 0 2px;
        font-size: 12px;
    }

    .close-btn {
        top: 15px;
        right: 20px;
        font-size: 28px;
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    #editModal .modal-content {
        width: 98%;
        margin: 1% auto;
        border-radius: 12px;
    }

    #editModal h3 {
        padding: 18px 20px;
        font-size: 18px;
    }

    #editModal form {
        padding: 20px 15px;
    }

    #editModal .form-group input,
    #editModal .form-group select {
        padding: 12px 15px;
        font-size: 16px;
    }

    table th,
    table td {
        padding: 6px;
        font-size: 11px;
    }

    .filter-input {
        padding: 4px;
        font-size: 11px;
    }

    .btn-add {
        font-size: 0.9rem;
        padding: 10px 20px;
        margin-bottom: 15px;
        margin-right: 8px;
    }

    .clear-filters-btn {
        font-size: 11px;
        padding: 6px 12px;
        margin-left: 3px;
    }

    .close-btn {
        top: 12px;
        right: 15px;
        font-size: 24px;
        width: 30px;
        height: 30px;
    }
}

/* Touch-friendly enhancements for mobile */
@media (hover: none) and (pointer: coarse) {
    .btn-add,
    .clear-filters-btn,
    .pagination-button {
        min-height: 44px;
        min-width: 44px;
    }

    #editModal .form-group input,
    #editModal .form-group select {
        min-height: 44px;
    }

    .close-btn {
        min-height: 44px;
        min-width: 44px;
    }
}
    </style>
</head>

<body>
<header>
        <div class="header-left">
            <img src="logo-inner.png" alt="Logo" class="logo">
        </div>
        <div class="header-right">
        <div class="header-info">
            <span class="user-email"><?php echo htmlspecialchars($login_email ?? 'Guest'); ?></span>
            <button class="logout" onclick="logout()">Logout</button>
        </div>
        <button class="back-btn" onclick="window.location.href='login.php'">Home</button>
        </div>
    </header>
    <div class="container">
        <h2>User List</h2>
        <a href="add_user.php" class="btn-add">Add User</a>

        <!-- Clear Filters Button -->
        <button type="button" class="clear-filters-btn" onclick="clearAllFilters()">Clear All Filters</button>

        <!-- Users Table with Filters -->
        <div class="datatable-container">
            <form method="get" id="filter-form">
                <table id="datatable">
                    <thead>
                        <tr>
                            <th>S/N</th>
                            <th>Name
                                <input type="text" class="filter-input" name="name"
                                    value="<?= htmlspecialchars($_GET['name'] ?? '') ?>"
                                    placeholder="Filter by name..."
                                    style="width: 120px;"
                                    onchange="submitFilterForm()"
                                    oninput="debounceFilter(this)">
                            </th>
                            <th>Email
                                <input type="text" class="filter-input" name="email"
                                    value="<?= htmlspecialchars($_GET['email'] ?? '') ?>"
                                    placeholder="Filter by email..."
                                    style="width: 150px;"
                                    onchange="submitFilterForm()"
                                    oninput="debounceFilter(this)">
                            </th>
                            <th>Department
                                <input type="text" class="filter-input" name="department"
                                    value="<?= htmlspecialchars($_GET['department'] ?? '') ?>"
                                    placeholder="Filter by dept..."
                                    style="width: 120px;"
                                    onchange="submitFilterForm()"
                                    oninput="debounceFilter(this)">
                            </th>
                            <th>Branch
                                <input type="text" class="filter-input" name="branch"
                                    value="<?= htmlspecialchars($_GET['branch'] ?? '') ?>"
                                    placeholder="Filter by branch..."
                                    style="width: 100px;"
                                    onchange="submitFilterForm()"
                                    oninput="debounceFilter(this)">
                            </th>
                            <th>Type
                                <input type="text" class="filter-input" name="type"
                                    value="<?= htmlspecialchars($_GET['type'] ?? '') ?>"
                                    placeholder="Filter by type..."
                                    style="width: 100px;"
                                    onchange="submitFilterForm()"
                                    oninput="debounceFilter(this)">
                            </th>
                            <th>Status
                                <input type="text" class="filter-input" name="app"
                                    value="<?= htmlspecialchars($_GET['app'] ?? '') ?>"
                                    placeholder="Approved/Pending..."
                                    style="width: 120px;"
                                    onchange="submitFilterForm()"
                                    oninput="debounceFilter(this)">
                            </th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $index => $user): ?>
                        <tr>
                            <td><?= $startIndex + $index + 1 ?></td>
                            <td><?= htmlspecialchars($user['name']) ?></td>
                            <td><?= htmlspecialchars($user['email']) ?></td>
                            <td><?= htmlspecialchars($user['dept']) ?></td>
                            <td><?= htmlspecialchars($user['branch']) ?></td>
                            <td><?= htmlspecialchars($user['Type']) ?></td>
                            <td><?= $user['app'] == 1 ? 'Approved' : 'Pending' ?></td>
                            <td><a href="javascript:void(0)" onclick="editUser(<?= $user['user_id'] ?>)" title="Edit User">✎</a></td>
                        </tr>
                        <?php endforeach; ?>

                        <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 20px; color: #666;">
                                No users found matching the current filters.
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="pagination">
                    <?php
                    $queryParams = $_GET;

                    // Function to build URL with preserved filters
                    function buildPageUrl($page, $queryParams) {
                        $queryParams['page'] = $page;
                        return 'users.php?' . http_build_query($queryParams);
                    }

                    // Display pagination info
                    echo '<div style="margin-bottom: 10px; color: #666; font-size: 14px;">';
                    echo "Showing " . ($startIndex + 1) . " to " . min($startIndex + $itemsPerPage, $totalItems) . " of $totalItems users";
                    if ($totalPages > 1) {
                        echo " (Page $currentPage of $totalPages)";
                    }
                    echo '</div>';

                    // Display "Previous"
                    if ($currentPage > 1) {
                        echo '<a href="' . htmlspecialchars(buildPageUrl($currentPage - 1, $queryParams)) . '" class="pagination-button">Previous</a>';
                    } else {
                        echo '<span class="pagination-button disabled">Previous</span>';
                    }

                    // Simplified page number logic for better performance
                    if ($totalPages <= 7) {
                        // Show all pages if 7 or fewer
                        for ($i = 1; $i <= $totalPages; $i++) {
                            $class = 'pagination-button' . ($i == $currentPage ? ' active' : '');
                            echo '<a href="' . htmlspecialchars(buildPageUrl($i, $queryParams)) . '" class="' . $class . '">' . $i . '</a>';
                        }
                    } else {
                        // Show first page
                        $class = 'pagination-button' . (1 == $currentPage ? ' active' : '');
                        echo '<a href="' . htmlspecialchars(buildPageUrl(1, $queryParams)) . '" class="' . $class . '">1</a>';

                        // Show ellipsis if needed
                        if ($currentPage > 3) {
                            echo '<span class="pagination-button disabled">...</span>';
                        }

                        // Show current page and neighbors
                        $start = max(2, $currentPage - 1);
                        $end = min($totalPages - 1, $currentPage + 1);

                        for ($i = $start; $i <= $end; $i++) {
                            $class = 'pagination-button' . ($i == $currentPage ? ' active' : '');
                            echo '<a href="' . htmlspecialchars(buildPageUrl($i, $queryParams)) . '" class="' . $class . '">' . $i . '</a>';
                        }

                        // Show ellipsis if needed
                        if ($currentPage < $totalPages - 2) {
                            echo '<span class="pagination-button disabled">...</span>';
                        }

                        // Show last page
                        if ($totalPages > 1) {
                            $class = 'pagination-button' . ($totalPages == $currentPage ? ' active' : '');
                            echo '<a href="' . htmlspecialchars(buildPageUrl($totalPages, $queryParams)) . '" class="' . $class . '">' . $totalPages . '</a>';
                        }
                    }

                    // Display "Next"
                    if ($currentPage < $totalPages) {
                        echo '<a href="' . htmlspecialchars(buildPageUrl($currentPage + 1, $queryParams)) . '" class="pagination-button">Next</a>';
                    } else {
                        echo '<span class="pagination-button disabled">Next</span>';
                    }
                    ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Modal -->
   <!-- Edit Modal -->
<div id="editModal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close-btn" onclick="closeModal()">×</span>
        <h3>Edit User</h3>
        <form method="POST" id="editUserForm">
            <input type="hidden" name="user_id" id="user_id">
            <div class="form-group">
                <label>Email:</label>
                <span id="edit-email"></span>
            </div>
            <div class="form-group">
                <label>Name:</label>
                <span id="edit-name"></span>
            </div>
            <div class="form-group">
                <label for="edit-type">User Type</label>
                <select name="type" id="edit-type" required>
                    <option value="User">User</option>
                    <option value="Admin">Admin</option>
                    <option value="Dispatcher">Dispatcher</option>
                </select>
            </div>
            <div class="form-group">
                <label for="edit-department">Department</label>
                <select name="dept" id="edit-department" required>
                    <option value="">Select Department</option>
                    <?php
                    $departments = $pdo->query("SELECT * FROM Departments")->fetchAll(PDO::FETCH_ASSOC);
                    foreach ($departments as $dept) {
                        echo "<option value='{$dept['DeptCode']}'>{$dept['DeptName']}</option>";
                    }
                    ?>
                </select>
            </div>
            <div class="form-group">
                <label for="edit-branch">Branch</label>
                <select name="branch" id="edit-branch" required>
                    <option value="">Select Branch</option>
                    <?php
                    $branches = $pdo->query("SELECT * FROM branch")->fetchAll(PDO::FETCH_ASSOC);
                    foreach ($branches as $branch) {
                        echo "<option value='{$branch['BCode']}'>{$branch['City']}</option>";
                    }
                    ?>
                </select>
            </div>
            <div class="form-group password-group">
                <label for="edit-password">Password (Leave blank to keep the same)</label>
                <div class="password-wrapper">
                    <input type="password" name="password" id="edit-password">
                    <i class="fas fa-eye toggle-password" onclick="togglePasswordVisibility()"></i>
                </div>
            </div>
            <button type="submit" name="edit_user" class="btn-add">Update User</button>
        </form>
    </div>
</div>

    <script>
// Function to open the modal and populate it with user data
function editUser(userId) {
    fetch(`fetch_users.php?user_id=${userId}`)
        .then(response => response.json())
        .then(user => {
            if (user) {
                // Populate the form with user data
                document.getElementById('user_id').value = user.ID;
                document.getElementById('edit-email').textContent = user.email;
                document.getElementById('edit-name').textContent = user.name;
                document.getElementById('edit-type').value = user.Type;

                // Set selected department by name (auto-select based on dept name)
                setDropdownValueByText('edit-department', user.dept);

                // Set selected branch by name (auto-select based on branch name)
                setDropdownValueByText('edit-branch', user.branch);

                // Set password field to empty (do not show the actual password)
                document.getElementById('edit-password').value = user.password;

                // Open the modal
                document.getElementById('editModal').style.display = 'block';
                console.log(user);
            } else {
                alert('User not found');
            }
        })
        .catch(error => alert('Error fetching user data'));
}

// Function to set a dropdown value based on the visible text
function setDropdownValueByText(selectId, valueText) {
    let selectElement = document.getElementById(selectId);
    if (selectElement) {
        for (let option of selectElement.options) {
            if (option.text === valueText) {
                selectElement.value = option.value; // Set the value of the matching option
                break;
            }
        }
    }
}

// Close the modal
function closeModal() {
    document.getElementById('editModal').style.display = 'none';
}

function togglePasswordVisibility() {
    const passwordField = document.getElementById('edit-password');
    const eyeIcon = document.querySelector('.toggle-password');
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// Close modal if clicked outside of it
window.onclick = function (event) {
    if (event.target === document.getElementById('editModal')) {
        closeModal();
    }
};

// Logout function
function logout() {
    // Make an AJAX request to clear the session
    const xhr = new XMLHttpRequest();
    xhr.open('POST', 'logout.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onload = function () {
        if (xhr.status === 200) {
            // Redirect to the login page after session is cleared
            window.location.href = 'login.php';
        } else {
            console.error('Failed to log out');
        }
    };
    xhr.send();
}



// Filter and pagination functionality
let filterTimeout;
let isSubmitting = false;

// Debounced filter function for real-time filtering
function debounceFilter(input) {
    if (isSubmitting) return;

    clearTimeout(filterTimeout);

    // Add loading indicator
    input.classList.add('filter-loading');

    filterTimeout = setTimeout(function() {
        input.classList.remove('filter-loading');
        submitFilterForm();
    }, 800); // Increased to 800ms to reduce server load
}

// Submit filter form
function submitFilterForm() {
    if (isSubmitting) return;
    isSubmitting = true;

    const form = document.getElementById('filter-form');
    const formData = new FormData(form);
    const params = new URLSearchParams();

    // Build query parameters more efficiently
    for (let [key, value] of formData.entries()) {
        const trimmedValue = value.trim();
        if (trimmedValue !== '') {
            params.append(key, trimmedValue);
        }
    }

    // Reset to page 1 when filtering
    params.delete('page');

    // Navigate to filtered results
    const url = 'users.php' + (params.toString() ? '?' + params.toString() : '');
    window.location.href = url;
}

// Clear all filters
function clearAllFilters() {
    // Clear all filter inputs
    const filterInputs = document.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
        input.value = '';
    });

    // Navigate to unfiltered page
    window.location.href = 'users.php';
}

// Clear individual filter
function clearFilter(inputElement) {
    inputElement.value = '';
    submitFilterForm();
}

// Optimized event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Use event delegation for better performance
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('pagination-button') && !e.target.classList.contains('disabled') && !e.target.classList.contains('active')) {
            // Add loading spinner to clicked pagination button
            const originalText = e.target.textContent;
            e.target.innerHTML = '<span class="loading-spinner"></span>' + originalText;
            e.target.style.pointerEvents = 'none';
        }
    });

    // Use event delegation for filter inputs
    document.addEventListener('keypress', function(e) {
        if (e.target.classList.contains('filter-input') && e.key === 'Enter') {
            e.preventDefault();
            clearTimeout(filterTimeout);
            submitFilterForm();
        }
    });

    document.addEventListener('keydown', function(e) {
        if (e.target.classList.contains('filter-input') && e.key === 'Escape') {
            clearFilter(e.target);
        }
    });
});

</script>
</body>

</html>
